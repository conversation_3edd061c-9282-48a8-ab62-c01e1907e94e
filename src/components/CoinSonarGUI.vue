<script lang="ts" setup>
import {
  delay,
  useCoinSonar,
  useMapHelpers,
  playSFX,
  useSound,
  useGlobalInstructor,
  useBeacon,
} from '@composables';
import { useDialogStore, useMapStore } from '@stores';
import { useQuery } from '@tanstack/vue-query';
import { IMetalSonar, IMetalSonarPrice } from '@types';
import gsap from 'gsap';

type Step = (typeof STEPS)[keyof typeof STEPS];

const STEPS = {
  SELECT_CIRCLE: 1,
  SELECT_RADIUS: 2,
} as const;

const storeDialog = useDialogStore();
const storeMap = useMapStore();

const { coinSonar, lastLocations } = storeToRefs(storeMap);
const { fitBounds } = useMapHelpers();
const { openDialog, closeDialog } = useMicroRoute();
const { play, stop } = useSound();
const {
  hasBeacon,
  price,
  basePrice,
  sonarCircles,
  sonarPrices,
  isMultipleSonarCircles,
  getCoinSonarPrices,
} = useCoinSonar();
const { inUsingBeacon } = useBeacon();
const { t } = useI18n();
const { openUnifyInstructor, closeUnifyInstructor } = useGlobalInstructor();

useQuery({
  queryKey: ['coinSonarPrices'],
  queryFn: async () => {
    const { selectedCircle } = coinSonar.value;
    selectedCircle?.properties;
    if (!selectedCircle?.properties) return true;
    await getCoinSonarPrices(selectedCircle.properties._id);
    return true;
  },
  refetchInterval: 1000 * 5,
});

const showDialog = ref(false);
const step = ref<Step>(
  isMultipleSonarCircles.value ? STEPS.SELECT_CIRCLE : STEPS.SELECT_RADIUS
);
const slide = ref(sonarCircles.value[0]?.properties._id || '');
const viewRadius = ref(0);

function handleSelectCircle() {
  const circle = sonarCircles.value.find(
    (c) => c.properties._id === slide.value
  );
  storeMap.coinSonar.selectedCircle = circle;
  step.value = STEPS.SELECT_RADIUS;
  fitBoundsWithRadius();
}

function fitBoundsWithRadius() {
  const radius = coinSonar.value.selectedPrice?.radius || 0;
  const [lng, lat] = lastLocations.value;
  fitBounds({
    lng,
    lat,
    radius: radius * 1.2,
    padding: 25,
  });
}

function fitBoundsWithSelectedCircle() {
  const properties = coinSonar.value.selectedCircle?.properties;
  if (!properties) return;
  const { center, radius } = properties.freeCircle;

  fitBounds({
    lng: center.lng,
    lat: center.lat,
    radius,
  });
}

function handleSelectPrice(ms: IMetalSonarPrice) {
  storeMap.coinSonar.selectedPrice = ms;
  fitBoundsWithRadius();
}

watch(slide, (val) => {
  const circle = sonarCircles.value.find((c) => c.properties._id === val);
  storeMap.coinSonar.selectedCircle = circle;
  fitBoundsWithSelectedCircle();
});

function handleBack() {
  step.value = STEPS.SELECT_CIRCLE;
  fitBoundsWithSelectedCircle();
}

async function handleConfirmUse() {
  showDialog.value = false;
  viewRadius.value = coinSonar.value.selectedPrice?.radius || 0;
  gsap.to('.sonar-arrow-left', {
    opacity: 0,
    duration: 0.5,
  });

  openDialog('sonar_confirm', {
    selectedPrice: coinSonar.value.selectedPrice,
    selectedCircle: coinSonar.value.selectedCircle || sonarCircles.value[0],
    onClose: () => {
      closeDialog('sonar_confirm');
    },
    onBack: () => {
      closeDialog('sonar_confirm');
      showDialog.value = true;
    },
    onGetCrystals: async () => {
      closeDialog('sonar_confirm');
      storeDialog.showCoinSonarGUI = false;
      await delay(500);
      openDialog('insufficient_crystals');
    },
    onUsedMetalSonar: (data: IMetalSonar) => {
      closeDialog('sonar_confirm');
      coinSonar.value.selectedPrice = undefined;
      scanningAnimation(data);
    },
  });
}

async function scanningAnimation(data: IMetalSonar) {
  play('scan');
  gsap.to('.sonar-control', {
    opacity: 1,
    duration: 0.5,
  });
  openUnifyInstructor('sqkii', {
    agent: 'radar-scanning',
    bubbleText: t('METAL_SONAR_ACTIVATED'),
    hiddenAnims: true,
    sequences: [
      {
        message: t('METAL_SONAR_SEARCHING'),
        persistent: true,
      },
    ],
  });
  await delay(5000);
  gsap.to('.sonar-control', {
    opacity: 0,
    duration: 0.5,
  });
  await storeMap.fetchMetalSonar();
  stop();
  playSFX(data.found_coin ? 'scan-success' : 'scan-failed');
  openUnifyInstructor('sqkii', {
    agent: 'radar-scanning',
    bubbleText: t('METAL_SONAR_ACTIVATED'),
    sequences: [
      {
        message: data.found_coin
          ? t('METAL_SONAR_SEARCHED_POSITIVE')
          : t('METAL_SONAR_SEARCHED_NEGATIVE'),
        persistent: true,
      },
    ],
  });

  await delay(1000);
  play('default');
  await delay(3000);
  openUnifyInstructor('sqkii', {
    agent: 'radar-scanning',
    bubbleText: t('METAL_SONAR_SCAN_COMPLETED_BUBBLE'),
    hiddenAnims: true,
    sequences: [
      {
        message: t('METAL_SONAR_SCAN_COMPLETED_TEXT'),
        persistent: true,
      },
    ],
  });
  await delay(4000);
  storeDialog.showCoinSonarGUI = false;
  closeUnifyInstructor();
}

onMounted(async () => {
  await nextTick();
  await delay(500);
  coinSonar.value.selectedPrice = sonarPrices.value[0];
  if (isMultipleSonarCircles.value) {
    coinSonar.value.selectedCircle = sonarCircles.value[0];
    fitBoundsWithSelectedCircle();
  }
  showDialog.value = true;
  await delay(1000);
  fitBoundsWithRadius();
});
</script>
<template>
  <div class="coin-sonar-gui fixed fullscreen">
    <div
      class="sonar-control absolute z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full opacity-0 pointer-events-none"
    >
      <div class="sonar-radar">
        <div class="circle"></div>
        <div class="shadow"></div>
      </div>
      <div class="pointer-events-none line-horizontal">
        <div class="absolute ml-2 text-xs left-3/4 -translate-x-3/4">
          {{ viewRadius }}m
        </div>
      </div>
      <div class="pointer-events-none line-vertical"></div>
    </div>
    <Button
      class="sonar-arrow-left fixed top-3 left-3 z-[9999]"
      shape="square"
      variant="secondary"
      @click="storeDialog.showCoinSonarGUI = false"
    >
      <Icon name="arrow-left" />
    </Button>
    <q-dialog v-model="showDialog" position="bottom" seamless>
      <div
        class="coin-sonar-dialog-container pt-10"
        :style="{
          backgroundImage: ([STEPS.SELECT_CIRCLE, STEPS.SELECT_RADIUS] as number[]).includes(
            step
          )
            ? 'url(/imgs/bg-coin-sonar.png)'
            : 'none',
          height: inUsingBeacon ? '400px': '350px'
        }"
      >
        <Button
          shape="square"
          variant="purple"
          class="absolute left-5 -top-3 z-20"
          v-show="step === STEPS.SELECT_RADIUS && isMultipleSonarCircles"
          @click="handleBack"
        >
          <Icon name="arrow-left" />
        </Button>
        <div class="flex flex-col justify-center items-center">
          <div
            class="text-text-base font-bold text-center p-4 mb-5 w-full"
            :style="{
              background:
                'linear-gradient(270deg, rgba(135, 100, 242, 0.00) -37.14%, rgba(135, 100, 242, 0.30) 46.61%, rgba(141, 118, 211, 0.00) 125.87%)',
            }"
            v-html="
              step === STEPS.SELECT_CIRCLE
                ? t('SONAR_METAL_SELECTED_CIRCLE_TITLE')
                : t('SONAR_METAL_GUI_TITLE')
            "
          ></div>
          <section
            class="w-full h-full text-center"
            v-show="step === STEPS.SELECT_CIRCLE"
          >
            <div
              class="mb-2 text-sm text-center px-10"
              v-html="t('SONAR_METAL_SELECTED_CIRCLE_DESC')"
            ></div>
            <q-carousel
              v-model="slide"
              swipeable
              animated
              padding
              arrows
              class="bg-transparent mb-5"
            >
              <q-carousel-slide
                v-for="c in sonarCircles"
                :key="c.properties._id"
                :name="c.properties._id"
              >
                <div
                  class="text-sm p-4 bg-[#04081D] w-full rounded-xl"
                  v-html="
                    `${t('SILVERCOINPOPUP_COIN_1', {
                      NUMBER: c.properties.coin_number,
                      BRAND_NAME: c.properties.brand_name || 'Silver',
                    })}`
                  "
                ></div>
              </q-carousel-slide>
            </q-carousel>
            <Button
              :label="t('SONAR_METAL_SELECTED_CIRCLE_BUTTON')"
              class="!w-[210px]"
              @click="handleSelectCircle"
            />
          </section>
        </div>
        <section
          v-show="step === STEPS.SELECT_RADIUS"
          class="w-full h-[calc(100%-80px)] flex flex-col flex-nowrap justify-center items-center"
        >
          <div
            class="mb-4 text-sm text-center px-10"
            v-html="t('SONAR_METAL_GUI_DESC')"
          ></div>
          <q-btn-group class="mb-8 metal-group-prices">
            <div
              class="absolute rounded-lg -inset-1 border border-[#7b37e950]"
            ></div>
            <q-btn
              class="capitalize opacity-50 price"
              :class="{
                active: coinSonar.selectedPrice?.radius === r.radius,
              }"
              v-for="r in sonarPrices"
              :key="r.radius"
              :label="`${r.radius}m`"
              @click="handleSelectPrice(r)"
            />
          </q-btn-group>
          <div
            v-if="inUsingBeacon"
            class="mb-5 text-sm frame-discount-powerup ml-0 w-full text-center"
            v-html="t('SONAR_METAL_GUI_DESC_1')"
          ></div>
          <Button
            class="mb-5 mx-auto !w-[260px]"
            :title="
              t('BEACON_TRIAL_COIN_SONAR_BTN_USE', {
                RADIUS: coinSonar.selectedPrice?.radius,
              })
            "
            :resource-type="
              coinSonar.selectedPrice?.can_use_power_up
                ? 'metal-sonar'
                : 'dbs_crystal'
            "
            :old-amount="hasBeacon ? basePrice : undefined"
            :amount="price"
            @click="handleConfirmUse"
          />
        </section>
      </div>
    </q-dialog>
  </div>
</template>
<style lang="scss" scoped>
.metal-group-prices {
  box-shadow: unset;
  position: relative;
  .price {
    width: 70px;
    height: 42px;
    background: linear-gradient(
      180deg,
      rgba(147, 75, 218, 0.8) 0%,
      #511d85 100%
    );
    &.active {
      opacity: 1;
      border: 1px solid #b57eec;
      background: linear-gradient(
        180deg,
        rgba(147, 75, 218, 0.8) 0%,
        #511d85 100%
      );
    }
  }
}
.coin-sonar-gui {
  --background: 205, 205, 205;
  --size: 60px;
  --duration: 3s;
  --scale: 3;
  --opacity: 0.4;
  .sonar-radar {
    width: 100%;
    height: 100%;
    animation: spin 4s linear infinite;
    .circle {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: var(--size);
      height: var(--size);
      background: rgba(var(--background), 0.5);
      border-radius: 100%;
      border: 1px solid #ffffff70;
      animation: pulse-shadow var(--duration) calc(2 * var(--duration) / 3)
        linear infinite;
      &::after,
      &::before {
        content: '';
        width: 100%;
        height: 100%;
        position: absolute;
        background: rgba(var(--background), 0.5);
        border-radius: 100%;
      }
      &::after {
        animation: pulse var(--duration) linear infinite;
      }
      &::before {
        animation: pulse var(--duration) calc(var(--duration) / 3) linear
          infinite;
      }
    }

    .shadow {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(
        0deg,
        rgb(var(--background)) 50%,
        transparent 100%
      );
      opacity: 0.6;
      padding: 50%;
      border-radius: 100%;
      clip-path: polygon(50% 0, 100% 0, 100% 50%, 50% 50%);
      z-index: 1;
    }
  }

  .line-vertical {
    z-index: 10;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1px;
    height: 50px;
    background-color: #ffffff;
  }

  .line-horizontal {
    z-index: 10;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 1px;
    background-color: #ffffff;
    &::before {
      position: absolute;
      content: '';
      left: 0;
      top: -5px;
      width: 1px;
      height: 5px;
      background-color: #ffffff;
    }
    &::after {
      position: absolute;
      content: '';
      right: 0;
      top: -5px;
      width: 1px;
      height: 5px;
      background-color: #ffffff;
    }
  }
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: var(--opacity);
    }

    100% {
      transform: scale(var(--scale));
      opacity: 0;
    }
  }

  @keyframes pulse-shadow {
    0% {
      box-shadow: 0 0 0 0 rgba(var(--background), var(--opacity));
    }

    100% {
      box-shadow: 0 0 0 calc((var(--scale) - 1) * var(--size) / 2)
        rgba(var(--background), 0);
    }
  }

  @keyframes spin {
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
<style lang="scss">
.coin-sonar-dialog-container {
  margin: 0 30px;
  width: 100%;
  height: 250px;
  margin-bottom: 8px;
  background-image: url(/imgs/bg-coin-sonar.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  overflow: visible !important;
  .q-carousel {
    height: unset;
    background-color: unset;
    padding: 0 !important;
    width: 100%;
  }
}
</style>
