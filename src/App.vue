<script setup lang="ts">
import { CapitalandBlocker, CapitalandLoading } from '@components';
import { useMapStore, useUserStore, useVouchersStore } from '@stores';

const hasCheckToken = ref(false);
const storeUser = useUserStore();
const mapStore = useMapStore();
const storeVouchers = useVouchersStore();

const isMobile = computed(() => (window as any).mobileCheck());
const { isSyncedWithKee } = storeToRefs(storeVouchers);
const { isAuthenticated, user, fetchedSettings, features } =
  storeToRefs(storeUser);
const { _loading } = storeToRefs(mapStore);

const fetched = ref(false);

async function checkToken() {
  if (!isAuthenticated.value) {
    await storeUser.playAsGuest();
  }

  if (isAuthenticated.value) {
    await storeUser.fetchUser();
  }
  hasCheckToken.value = true;
  await storeUser.fetchCountryCode();

  await Promise.all([
    storeUser.fetchSetting(),
    storeUser.fetchAnnouncement(),
    storeUser.fetchContest(),
  ]);
  // storeUser.fetchEvents();
  await Promise.all([
    storeUser.fetchInventory(),
    storeUser.fetchListCrystalExpiring(),
    storeUser.fetchTranSaction(),
    storeUser.fetchAdventureLog(),
    storeUser.fetchPedometerProgress(),
    storeUser.fetchTimeline(),
    storeUser.fetchReferral(),
    storeUser.fetchHints(),
    storeUser.fetchDailyMission(),
    storeUser.fetchBeacon(),
    storeUser.fetchTotalSVSubmitted(),
    // for sentosa
    // storeUser.fetchSentosaDailyRewards(),
    // storeUser.fetchSentosaGoldenCoin(),
    // storeUser.fetchSentosaBeachCount(),
    // storeUser.drawSentosaBeachPolygon(),
    // storeUser.fetchSentosaIslandBounty(),
    // CAPITALAND

    // storeUser.fetchCapitalandDailyRewards(),
    // storeUser.fetchCapitalandGeneoCoin(),
    // storeUser.fetchCapitalandAmenities(),

    // DBS
  ]);

  if (!!features.value?.sqkii_voucher) storeVouchers.fetchOutlets();

  if (isSyncedWithKee.value) {
    // TO DO: for sqkii vouchers
    await storeVouchers.fetchUser();
  }
  fetched.value = true;
}

checkToken();

watch(
  () => user.value?.mobile_number,
  (value) => {
    if (value) checkToken();
  }
);
</script>
<template>
  <CapitalandBlocker v-if="!isMobile" />
  <template v-else>
    <q-dialog
      :model-value="_loading"
      fullscreen
      position="standard"
      transition-show="none"
      transition-hide="fade"
    >
      <CapitalandLoading :fetched="fetched && fetchedSettings" />
    </q-dialog>
    <RouterView v-if="hasCheckToken" />
  </template>
</template>
