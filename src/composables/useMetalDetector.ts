import { useQuery } from '@tanstack/vue-query';
import { useDialogStore, useMapStore, useUserStore } from '@stores';
import { useInventory, useMapHelpers, useTick } from '@composables';
import { MAP } from '@repositories';
import { errorNotify } from '@helpers';

const METAL_DETECTOR_CONFIG = {
  REFETCH_INTERVAL: 5000, // 5 seconds
  DEFAULT_RADIUS: 30,
  RADIUS_PADDING_MULTIPLIER: 1.2,
} as const;

export function useMetalDetector(intervalPrices = false) {
  const storeUser = useUserStore();
  const storeMap = useMapStore();
  const storeDialog = useDialogStore();

  const { inventoryItem } = storeToRefs(storeDialog);
  const { settings, crystals, user, onboarding, isEnabledGPS } =
    storeToRefs(storeUser);
  const { lastLocations } = storeToRefs(storeMap);

  const { now } = useTick();
  const { closeAllDialog, openDialog } = useMicroRoute();
  const { fitBounds } = useMapHelpers();
  const { t } = useI18n();
  const { itemsQuickView } = useInventory();

  const loading = ref(false);
  const locationWhenCheckPrice = ref([0, 0]);

  const isUserOnCooldown = (): boolean => {
    if (!user.value?.metal_detector_cooldown) return false;
    return new Date(user.value.metal_detector_cooldown).getTime() > now.value;
  };

  const isWithinActiveTimeWindow = (): boolean => {
    if (!settings.value?.metal_detector) return false;
    const { active_at, inactive_at } = settings.value.metal_detector;
    return (
      now.value > new Date(active_at).getTime() &&
      now.value < new Date(inactive_at).getTime()
    );
  };

  const itemId = computed(() => inventoryItem.value?._id);

  const hasPowerUpItem = computed(() => {
    return itemsQuickView.value.metalDetector.length > 0;
  });

  const isTriggerMetalDetector = computed(() => {
    if (!settings.value?.metal_detector || !user.value) return false;
    if (isUserOnCooldown()) return false;
    return isWithinActiveTimeWindow();
  });

  const fetchMetalDetectorPrice = async () => {
    try {
      locationWhenCheckPrice.value = [...lastLocations.value];
      const [lng, lat] = locationWhenCheckPrice.value;

      const { data } = await MAP.getMetalDetectorPrice({ lat, lng });
      return data;
    } catch (error) {
      console.error('error', error);
    }
  };

  const { data: discount } = useQuery({
    queryKey: ['metalDetectorPrices', lastLocations],
    queryFn: fetchMetalDetectorPrice,
    enabled: intervalPrices,
    refetchInterval: METAL_DETECTOR_CONFIG.REFETCH_INTERVAL,
  });

  const hasBeacon = computed(() => !!discount.value?.beacon);
  const price = computed(() => discount.value?.price ?? 0);
  const basePrice = computed(() => discount.value?.base_price ?? 0);

  const canAffordMetalDetector = (): boolean => {
    return crystals.value >= price.value || !!itemId.value;
  };

  const validateGPSEnabled = (): boolean => {
    if (!isEnabledGPS.value) {
      errorNotify({ message: t('GPS_ERROR_NOT_DETECTED') });
      return false;
    }
    return true;
  };

  const handleRequestMetalDetector = (): void => {
    try {
      loading.value = true;

      if (!canAffordMetalDetector()) {
        closeAllDialog();
        openDialog('insufficient_crystals');
        return;
      }

      closeAllDialog();
      storeDialog.showMetalDetectorGUI = true;
    } catch (error) {
      errorNotify({ message: t('METAL_DETECTOR_REQUEST_ERROR') });
    } finally {
      loading.value = false;
    }
  };

  const handleBackToMain = async (): Promise<void> => {
    try {
      storeUser.metalDetectorItemId = '';
      storeDialog.showMetalDetectorGUI = false;
      closeAllDialog();
    } catch (error) {
      console.error('error', error);
    }
  };

  const fitBoundsWithLocation = (): void => {
    const [lng, lat] = lastLocations.value;
    fitBounds({
      lng,
      lat,
      radius:
        METAL_DETECTOR_CONFIG.DEFAULT_RADIUS *
        METAL_DETECTOR_CONFIG.RADIUS_PADDING_MULTIPLIER,
    });
  };

  const metalDetectorAction = (): void => {
    if (!validateGPSEnabled()) return;

    if (!onboarding.value?.first_metal_detector) {
      openDialog('metal_detector_welcome');
      return;
    }

    openDialog('metal_detector_warning_popup');
  };

  return {
    lastLocations,
    loading,
    isTriggerMetalDetector,
    hasBeacon,
    price,
    basePrice,
    itemId,
    hasPowerUpItem,

    handleRequestMetalDetector,
    handleBackToMain,
    fitBoundsWithLocation,
    metalDetectorAction,
  };
}
