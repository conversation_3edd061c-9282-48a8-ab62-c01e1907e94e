import { useDialogStore, useMapStore, useUserStore } from '@stores';
import { delay, useBAStatus, useGlobal, useInventory } from '@composables';
import { last } from 'lodash';
import { errorNotify, isDifferentDay } from '@helpers';

interface DialogTrigger {
  dialogPath: string;
  props?: Record<string, unknown>;
  condition: () => boolean;
  action?: () => Promise<void>;
}

interface TriggerState {
  triggeredIds: Set<string>;
  isProcessing: boolean;
  pendingTrigger: boolean;
  gpsErrorShown: boolean;
}

export function useGlobalTriggerDialog() {
  const SUBSEQUENT_TRIGGER_DELAY = 200;

  const storeMap = useMapStore();
  const storeDialog = useDialogStore();
  const storeUser = useUserStore();

  const { settings, isEnabledGPS, hasTriggerGPS, isDifferentCountry } =
    storeToRefs(storeUser);
  const { _loading } = storeToRefs(storeMap);
  const {
    showCoinSonarGUI,
    showBeaconGUI,
    showMetalDetectorGUI,
    showInventoryQuickAccess,
    welcomeSafetyDate,
  } = storeToRefs(storeDialog);
  const {
    triggerBuildUpdate,
    isTriggerSubDomain,
    isTriggerEnterSerialNumber,
    isTriggerAnnouncement,
    checkVerifications,
  } = useGlobal();
  const { checkBaStatus, showNotiBaSatus, checkedBaStatus } = useBAStatus();
  const { dialogs, currentPath, openDialog, push } = useMicroRoute();
  const { redirectedFrom } = useRoute();
  const { canTriggerInventoryOnboarding, triggerFirstBagOnboarding } =
    useInventory();
  const { t } = useI18n();

  const state = reactive<TriggerState>({
    triggeredIds: new Set(),
    isProcessing: false,
    pendingTrigger: false,
    gpsErrorShown: false,
  });

  const activeDialog = computed(() => dialogs.value.some((d) => d.actived));
  const activePage = computed(() => last(currentPath.value.split('/')));
  const activePowerUp = computed(() => {
    return [
      showCoinSonarGUI.value,
      showBeaconGUI.value,
      showMetalDetectorGUI.value,
      showInventoryQuickAccess.value,
    ].some(Boolean);
  });

  const isIdleInHome = computed(() => {
    const isHomeScreen = activePage.value === 'home';
    const noDialog = !activeDialog.value;
    if (_loading.value || activePowerUp.value) return false;
    return [isHomeScreen, noDialog].every(Boolean);
  });

  const canTriggerNotiBaStatus = computed(() => {
    return checkBaStatus.value && !checkedBaStatus.value;
  });

  // Registry of dialog triggers: priority is determined by the order of the array
  const triggers = computed<DialogTrigger[]>(() => {
    const recoverToken = (): string => {
      if (!redirectedFrom) return '';
      if (!redirectedFrom.query.recover_token) return '';
      return String(redirectedFrom.query.recover_token);
    };

    const topUpStatus = (): string => {
      if (!redirectedFrom) return '';
      if (!redirectedFrom.query.status) return '';
      return String(redirectedFrom.query.status);
    };

    const topUpId = (): string => {
      if (!redirectedFrom) return '';
      if (!redirectedFrom.query.reference) return '';
      return String(redirectedFrom.query.reference);
    };

    const canTriggerTAC = (): boolean => {
      if (!redirectedFrom) return false;
      return redirectedFrom.path === '/terms';
    };

    const canTriggerFoundDialog = (): boolean => {
      if (!redirectedFrom) return false;
      return redirectedFrom.path === '/found';
    };

    const canTriggerSonarTerms = (): boolean => {
      if (!redirectedFrom) return false;
      return redirectedFrom.path === '/sonar';
    };

    const canTriggerHoldingPage = (): boolean => {
      if (process.env.IS_TESTING_ENV) return false;
      return (
        !!settings.value?.holding_page_state &&
        settings.value?.holding_page_state !== 'none'
      );
    };

    const canTriggerMainOnboarding = computed(() => {
      return false;
    });

    const canTriggerWelcomeSafety = computed(() => {
      if (!welcomeSafetyDate.value) return true;
      const date = new Date().toISOString();
      return isDifferentDay(date, welcomeSafetyDate.value.toString());
    });

    return [
      {
        dialogPath: 'top_up_result',
        props: {
          status: topUpStatus(),
          id: topUpId(),
        },
        condition: () => Boolean(topUpStatus()) && Boolean(topUpId()),
        action: async () => {
          push('sqkii_vouchers');
          await delay(1000);
        },
      },
      {
        dialogPath: 'kee_recover_pw',
        props: {
          recover_token: recoverToken(),
        },
        condition: () => Boolean(recoverToken()),
        action: async () => {
          push('sqkii_vouchers');
          await delay(1000);
        },
      },
      {
        dialogPath: 'enter_serial_number',
        condition: canTriggerFoundDialog,
      },
      {
        dialogPath: 'sonar_terms',
        condition: canTriggerSonarTerms,
      },
      {
        dialogPath: 'tac',
        condition: canTriggerTAC,
      },
      {
        dialogPath: 'holding_page',
        condition: canTriggerHoldingPage,
        action: async () => {
          openDialog('holding_page');
        },
      },
      {
        dialogPath: 'build_update',
        condition: () => triggerBuildUpdate.value,
      },
      {
        dialogPath: 'onboarding_golden',
        condition: () => canTriggerMainOnboarding.value,
      },
      {
        dialogPath: 'welcome_safety',
        condition: () => canTriggerWelcomeSafety.value,
      },
      {
        dialogPath: 'sub_domain',
        condition: () => isTriggerSubDomain.value,
      },
      {
        dialogPath: 'enter_serial_number',
        props: {
          stage: 'continue',
        },
        condition: () => isTriggerEnterSerialNumber.value,
      },
      {
        dialogPath: 'announcement',
        condition: () => isTriggerAnnouncement.value,
      },
    ];
  });

  const getId = (trigger: DialogTrigger): string => trigger.dialogPath;

  const canTrigger = (trigger: DialogTrigger): boolean => {
    const id = getId(trigger);
    return !state.triggeredIds.has(id) && trigger.condition();
  };

  const triggerDialog = async (trigger: DialogTrigger): Promise<void> => {
    if (trigger.action) await trigger.action();
    if (!!trigger.dialogPath) openDialog(trigger.dialogPath, trigger.props);
  };

  const executeTrigger = async (trigger: DialogTrigger): Promise<void> => {
    const id = getId(trigger);
    state.triggeredIds.add(id);
    await triggerDialog(trigger);
  };

  const evaluateTriggers = async (): Promise<void> => {
    if (state.isProcessing) return;

    if (!isIdleInHome.value && !state.pendingTrigger) return;

    state.isProcessing = true;
    state.pendingTrigger = false;

    try {
      const sortedTriggers = triggers.value
        .map((trigger, index) => ({ trigger, index }))
        .sort((a, b) => a.index - b.index)
        .map((item) => item.trigger);

      for (const trigger of sortedTriggers) {
        if (canTrigger(trigger)) {
          await executeTrigger(trigger);
          break;
        }
      }
    } finally {
      state.isProcessing = false;
    }
  };

  const handleDelayedTrigger = async (ms: number): Promise<void> => {
    await delay(ms);
    await evaluateTriggers();
  };

  const isErrorGPS = computed(() => {
    return !isEnabledGPS.value && hasTriggerGPS.value;
  });

  const handleErrorGPS = () => {
    if (state.gpsErrorShown || !isErrorGPS.value) return;
    state.gpsErrorShown = true;
    storeMap.geoState = 'UNAVAILABLE';
    errorNotify({
      message: !isDifferentCountry.value
        ? t('GPS_ERROR_NOT_DETECTED')
        : t('GPS_ERROR_COUNTRY_NOT_DETECTED'),
      timeout: 0,
    });
  };

  watch(
    isIdleInHome,
    async (isIdle) => {
      if (!isIdle) return;

      // Verifications
      await checkVerifications();

      handleDelayedTrigger(SUBSEQUENT_TRIGGER_DELAY);

      // Error GPS
      handleErrorGPS();

      // BA Status
      if (canTriggerNotiBaStatus.value) showNotiBaSatus();

      // Inventory Onboarding
      if (canTriggerInventoryOnboarding.value) triggerFirstBagOnboarding();
    },
    { immediate: true }
  );

  watch(
    () => state.pendingTrigger,
    async (isPending) => {
      if (isPending) await evaluateTriggers();
    }
  );
}
