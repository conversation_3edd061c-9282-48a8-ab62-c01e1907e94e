import { useDialogStore, useMapStore, useUserStore } from '@stores';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import { point, Polygon } from '@turf/helpers';
import { useInventory, useTick } from '@composables';
import { IAPIResponseError, IErrorMsg } from '@types';
import { errorNotify } from '@helpers';
import { METAL_SONAR } from '@repositories';

export function useCoinSonar() {
  const storeDialog = useDialogStore();
  const storeMap = useMapStore();
  const storeUser = useUserStore();

  const { lastLocations, groupedSilverCoins, coinSonar } =
    storeToRefs(storeMap);
  const { showCoinSonarGUI } = storeToRefs(storeDialog);
  const { user, settings, isEnabledGPS } = storeToRefs(storeUser);
  const { itemsQuickView } = useInventory();
  const { now } = useTick();
  const { t } = useI18n();
  const { openDialog } = useMicroRoute();

  const locations = reactive({
    lat: 0,
    lng: 0,
  });

  const selectedPriceObj = computed(() => {
    if (!coinSonar.value.dataPrices) return undefined;
    return coinSonar.value.dataPrices.prices.find(
      (p) => p.radius === coinSonar.value.selectedPrice?.radius
    );
  });

  const price = computed(() => selectedPriceObj.value?.price ?? 0);

  const basePrice = computed(() => selectedPriceObj.value?.base_price ?? 0);

  const sonarCircles = computed(() => {
    const ongoingCoins = groupedSilverCoins.value.ongoing;
    if (!ongoingCoins.length) return [];
    const pt = point(lastLocations.value);

    return ongoingCoins.filter(
      (c) =>
        booleanPointInPolygon(pt, c as unknown as Polygon) &&
        c.properties.is_smallest_public_circle
    );
  });

  const isFirstCoinSonar = computed(
    () => !user.value?.onboarding?.first_metal_sonar
  );

  const isInPolygon = computed(() => {
    return sonarCircles.value.length > 0;
  });

  const isMultipleSonarCircles = computed(() => {
    return sonarCircles.value.length > 1;
  });

  const hasPowerUpItem = computed(() => {
    return itemsQuickView.value.coinSonar.length > 0;
  });

  const canTriggerCoinSonar = computed(() => {
    if (!settings.value?.metal_sonar || !sonarCircles.value.length)
      return false;

    return (
      now.value > +new Date(settings.value.metal_sonar.start_at) &&
      now.value < +new Date(settings.value.metal_sonar.end_at) &&
      sonarCircles.value.length > 0
    );
  });

  const hasBeacon = computed(() => {
    return !!coinSonar.value.dataPrices?.beacon;
  });

  const sonarPrices = computed(() => {
    const prices = coinSonar.value.dataPrices?.prices ?? [];
    const coinSonarItems = itemsQuickView.value.coinSonar;
    return prices.map((p) => ({
      ...p,
      can_use_power_up: coinSonarItems.some((pu) => pu.radius === p.radius),
      item_id: coinSonarItems.find((pu) => pu.radius === p.radius)?._id,
    }));
  });

  async function getCoinSonarPrices(id: string) {
    try {
      const [lng, lat] = lastLocations.value;
      locations.lat = lat;
      locations.lng = lng;

      const { data } = await METAL_SONAR.getPrices({
        id,
        lng,
        lat,
      });
      storeMap.coinSonar.dataPrices = data;
    } catch (error) {
      handleError(error as IAPIResponseError);
    }
  }

  async function handleRequestCoinSonar(triggerGUI = false) {
    if (!isEnabledGPS.value)
      return errorNotify({
        message: t('GPS_ERROR_NOT_DETECTED'),
      });

    if (!isInPolygon.value)
      return errorNotify({
        message: t('SONAR_ERROR_NOT_IN_POLYGON'),
      });

    const id = sonarCircles.value[0].properties._id;

    if (isFirstCoinSonar.value) {
      return openDialog('metal_sonar_welcome', {
        onLetGo: async () => {
          await getCoinSonarPrices(id);
          if (triggerGUI) showCoinSonarGUI.value = true;
        },
      });
    }

    await getCoinSonarPrices(id);
    if (triggerGUI) showCoinSonarGUI.value = true;
  }

  function handleError(error: IAPIResponseError) {
    const { error_message } = error;

    const messages = {
      no_coin: t('SONAR_TERMS_ERROR_NO_COIN'),
      no_event: t('SONAR_TERMS_ERROR_NO_EVENT'),
    } as Record<IErrorMsg, string>;

    errorNotify({
      message: messages[error_message] || error_message,
    });
  }

  return {
    sonarCircles,
    isInPolygon,
    isMultipleSonarCircles,
    canTriggerCoinSonar,
    hasBeacon,
    hasPowerUpItem,
    sonarPrices,
    showCoinSonarGUI,
    price,
    basePrice,
    getCoinSonarPrices,
    handleRequestCoinSonar,
    handleError,
  };
}
