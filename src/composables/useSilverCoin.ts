import { useQuery } from '@tanstack/vue-query';
import { ref, computed, readonly, watch, nextTick } from 'vue';
import { LocalStorage } from 'quasar';
import {
  delay,
  useClick,
  useCoinSonar,
  useTick,
  useBrandDoodleSov,
  useBrandSov,
  useGlobalInstructor,
} from '@composables';
import { useBAStore, useMapStore, useUserStore } from '@stores';
import { useTrackData } from '@composables';
import {
  closeNotify,
  errorNotify,
  getSovAsset,
  timeCountDown,
  tryShare2,
} from '@helpers';
import { MAP } from '@repositories';
import { BRAND_SOV, InventoryItemType } from '@constants';
import type {
  InventoryItem,
  ISilverCoin,
  SilverCoinPowerUpSelection,
} from '@types';

interface FirstTimeExperienceState {
  isFirstTap: boolean;
  isFirstMetalSonar: boolean;
}

export function useSilverCoinSov() {
  const shrinkTag = computed(() => {
    const { randomResult, ready } = useBrandSov(
      'silver_coin_circle_shrinks_logo',
      {
        id: 'silver_coin_circle_shrinks_character',
        track: false,
      }
    );
    return { randomResult, ready };
  });

  const shrinkAgent = computed(() => {
    if (!shrinkTag.value) return null;
    const doodle = useBrandDoodleSov({
      ready: shrinkTag.value.ready,
      randomResult: shrinkTag.value.randomResult,
      referenceId: 'silver_coin_circle_shrinks_logo',
      doodleId: 'silver_coin_circle_shrinks_character',
    });
    return doodle.value;
  });

  const shrinkSovContext = computed(() => {
    if (!shrinkTag.value?.randomResult) return null;
    if (!shrinkAgent.value) return null;

    return {
      agent: getSovAsset(
        'character',
        shrinkAgent.value.brand_unique_id ?? BRAND_SOV.DBS,
        '_nancii'
      ),
      tag: shrinkTag.value.randomResult.value.silver_coin_circle_shrinks_logo.getAsset(),
    };
  });

  return {
    shrinkSovContext,
  };
}

export function useSilverCoin(
  coin: ComputedRef<ISilverCoin>,
  inventoryItem?: ComputedRef<InventoryItem>
) {
  const coinRef = computed(() => coin.value);
  const inventoryItemRef = computed(() => inventoryItem?.value);

  const storeUser = useUserStore();
  const storeBA = useBAStore();
  const storeMap = useMapStore();

  const { user, settings, crystals, features, onboarding, inventoryData } =
    storeToRefs(storeUser);
  const { lastLocations, silverCoinById } = storeToRefs(storeMap);
  const { newBrandActions } = storeToRefs(storeBA);

  const { openDialog, closeDialog } = useMicroRoute();
  const { now } = useTick();
  const { t } = useI18n();
  const { track } = useTrackData();
  const { openUnifyInstructor, closeUnifyInstructor } = useGlobalInstructor();
  const { handleRequestCoinSonar } = useCoinSonar();

  const inventoryItems = computed(() => inventoryData.value.items);

  const itemsQuickView = computed(() => {
    const getTime = (expires_at: string) => new Date(expires_at).getTime();
    const getItemsByType = (itemType: InventoryItemType) =>
      inventoryItems.value
        .filter((item) => item.item_type === itemType)
        .sort((a, b) => getTime(a.expires_at) - getTime(b.expires_at));

    return {
      shrink: getItemsByType(InventoryItemType.SHRINK),
      shrinkLite: getItemsByType(InventoryItemType.SHRINK_LITE),
      coinSonar: getItemsByType(InventoryItemType.COIN_SONAR),
      metalDetector: getItemsByType(InventoryItemType.METAL_DETECTOR),
      beacon: getItemsByType(InventoryItemType.BEACON),
    };
  });

  const selectedPowerUp = ref<SilverCoinPowerUpSelection>();

  const itemId = computed(() => {
    if (inventoryItemRef.value) return inventoryItemRef.value._id;
    return selectedPowerUp.value?.item_id;
  });

  const locationWhenCheckPrice = ref<{ lat: number; lng: number }>();
  const firstTimeState = ref<FirstTimeExperienceState>({
    isFirstTap: !!LocalStorage.getItem('first_tap_silvercoin'),
    isFirstMetalSonar: !!LocalStorage.getItem('first_metal_sonar'),
  });

  const resourceType = computed(() => {
    if (!selectedPowerUp.value) return 'dbs_crystal';
    const typeMap = {
      [InventoryItemType.SHRINK]: 'silver-shrink',
      [InventoryItemType.SHRINK_LITE]: 'silver-shrink-lite',
      [InventoryItemType.COIN_SONAR]: 'metal-sonar',
      [InventoryItemType.METAL_DETECTOR]: 'metal-detector',
    };
    const { type, total } = selectedPowerUp.value;
    return total > 0 ? typeMap[type as keyof typeof typeMap] : 'dbs_crystal';
  });

  const coinData = computed(() => {
    return silverCoinById.value[coinRef.value._id] ?? coinRef.value;
  });

  const nextShrinkCountdown = computed(() => {
    if (!coinData.value.nextShrinkAt) return;
    const cd = +new Date(coinData.value.nextShrinkAt);
    if (cd < now.value) return;
    return timeCountDown(cd - now.value);
  });

  const shrinkPowerUpCountdown = computed(() => {
    const userShrinkCd = user.value?.shrink_cooldown?.[coinRef.value._id] || 0;
    const cooldown = Math.max(0, +new Date(userShrinkCd) - now.value);
    const lockThreshold = settings.value?.ms_cooldown_to_lock || 0;

    let lockTime = 0;
    if (cooldown > lockThreshold) lockTime = cooldown - lockThreshold;

    return lockTime;
  });

  const powerUpCountdownText = computed(() => {
    return timeCountDown(shrinkPowerUpCountdown.value);
  });
  const canUsePowerUp = computed(() => coinRef.value.canUsePowerUp);
  const isSmallestPublicCircle = computed(
    () => coinData.value.is_smallest_public_circle
  );
  const isFreeShrink = computed(() => !onboarding.value?.shrink_silver_coin);
  const hasPaidCircle = computed(() => !!coinRef.value.paidCircle.radius);
  const timeLeftText = computed(() =>
    timeCountDown(+new Date(coinRef.value.end_at) - now.value)
  );

  const currentCoinWithBA = computed(() => {
    if (!newBrandActions.value.length) {
      return {
        ...coinRef.value,
        dataBA: undefined,
      };
    }

    const activeBrandAction = newBrandActions.value.find(
      (item) =>
        (!item.release_date || +new Date(item.release_date) < Date.now()) &&
        (!item.close_date || +new Date(item.close_date) > Date.now())
    );

    return {
      ...coinRef.value,
      dataBA: activeBrandAction,
    };
  });

  const isTriggerMetalSonar = computed(() => {
    const start_at = settings.value?.metal_sonar?.start_at;
    const end_at = settings.value?.metal_sonar?.end_at;
    if (!start_at || !end_at) return false;

    return (
      now.value > +new Date(start_at) &&
      now.value < +new Date(end_at) &&
      !!coinRef.value.is_smallest_public_circle
    );
  });

  const shouldShowMetalSonarIntro = computed(() => {
    return (
      isTriggerMetalSonar.value &&
      !firstTimeState.value.isFirstMetalSonar &&
      firstTimeState.value.isFirstTap
    );
  });

  const fetchShrinkPrice = async () => {
    locationWhenCheckPrice.value = {
      lat: lastLocations.value[1],
      lng: lastLocations.value[0],
    };

    const { data } = await MAP.getShrinkPrice({
      coin_id: coinRef.value._id,
      lat: locationWhenCheckPrice.value.lat,
      lng: locationWhenCheckPrice.value.lng,
    });
    return data;
  };

  const { data: discount } = useQuery({
    queryKey: ['getShrinkPrices'],
    queryFn: fetchShrinkPrice,
    refetchInterval: 5000,
    staleTime: 15000,
  });

  const shrinkPrice = computed(() => {
    if (!settings.value) return 0;
    return discount.value?.beacon
      ? discount.value.price
      : settings.value.shrink_circle_price;
  });

  const hasDiscount = computed(() => !!discount.value?.beacon);
  const originalPrice = computed(
    () => settings.value?.shrink_circle_price || 0
  );

  const canShrinkWithItem = computed(() => {
    if (inventoryItemRef.value) {
      const itemTypeToInventory = {
        [InventoryItemType.SHRINK_LITE]: itemsQuickView.value.shrinkLite,
        [InventoryItemType.SHRINK]: itemsQuickView.value.shrink,
        [InventoryItemType.COIN_SONAR]: itemsQuickView.value.coinSonar,
        [InventoryItemType.METAL_DETECTOR]: itemsQuickView.value.metalDetector,
        [InventoryItemType.BEACON]: itemsQuickView.value.beacon,
      };

      const relevantInventory =
        itemTypeToInventory[inventoryItemRef.value.item_type];
      return relevantInventory ? relevantInventory.length > 0 : false;
    }

    return (selectedPowerUp.value?.total ?? 0) > 0;
  });

  const canAffordShrink = computed(() => {
    if (!user.value) return false;
    if (isFreeShrink.value || canShrinkWithItem.value) return true;
    return user.value.resources.crystal >= shrinkPrice.value;
  });

  const canActiveShrink = computed(() => {
    return !!features.value?.shrink_power_up;
  });

  const markFirstTapComplete = () => {
    firstTimeState.value.isFirstTap = true;
    LocalStorage.set('first_tap_silvercoin', true);
  };

  const markFirstMetalSonarComplete = () => {
    firstTimeState.value.isFirstMetalSonar = true;
    LocalStorage.set('first_metal_sonar', true);
  };

  const trackAction = (
    action: string,
    extraData: Record<string, unknown> = {}
  ) => {
    track(action, {
      circle_no: coinRef.value._id,
      circle_size: coinRef.value.circle.radius,
      ...extraData,
    });
  };

  const handleShrinkCircle = () => {
    trackAction('shrink_circle_button', {
      circle_size: currentCoinWithBA.value.circle.radius,
      user_crystals: crystals.value,
    });

    if (isFreeShrink.value)
      trackAction('free_circle_chrink_popup', { action: 'free_shrink_circle' });

    markFirstTapComplete();
    closeUnifyInstructor();

    if (!user.value || !settings.value || !features.value?.shrink_power_up)
      return;

    if (!canAffordShrink.value && !isFreeShrink.value) {
      openDialog('insufficient_crystals', {
        dataBA: currentCoinWithBA.value.dataBA,
        onClose: () => {
          closeDialog('insufficient_crystals');
          // if (shouldShowMetalSonarIntro.value) checkWithFirstMetalSonar();
        },
      });
    } else {
      const powerUpType =
        selectedPowerUp.value?.type ||
        inventoryItemRef.value?.item_type ||
        InventoryItemType.SHRINK;

      openDialog('use_shrink', {
        powerUpType,
        itemId: itemId.value,
        coin: coinRef.value,
        location: locationWhenCheckPrice.value,
        discount: discount.value,
        onClose: () => {
          closeDialog('use_shrink');
          if (shouldShowMetalSonarIntro.value) checkWithFirstMetalSonar();
        },
      });
    }
  };

  const checkWithFeaturedirstTapCoin = async () => {
    await delay(500);
    const AMOUNT =
      Number(coinRef.value.freeCircle.radius) -
      Number(coinRef.value.paidCircle.radius || 0);
    openUnifyInstructor('timii', {
      sequences: [
        {
          target: '#general-dialog',
          message: t('TIMII_FIRST_TAP_COIN', {
            AMOUNT,
          }),
          actions: {
            cb: (_, close) => {
              close();
              markFirstTapComplete();
              // if (shouldShowMetalSonarIntro.value) checkWithFirstMetalSonar();
            },
            closeX: (_, close) => {
              close();
              markFirstTapComplete();
              // if (shouldShowMetalSonarIntro.value) checkWithFirstMetalSonar();
            },
          },
        },
      ],
    });
  };

  const handleClickCountDown = () => {
    trackAction('shrink_circle_button_countdown', {
      circle_size: currentCoinWithBA.value.circle.radius,
      user_crystals: crystals.value,
    });
    closeUnifyInstructor();
    return openUnifyInstructor('timii', {
      sequences: [
        {
          message: t('SILVERCOINPOPUP_NOSHRINK_TEXTBOX', {
            TIME: powerUpCountdownText.value,
          }),
        },
      ],
    });
  };

  const checkWithFirstMetalSonar = async () => {
    await delay(500);
    openUnifyInstructor('timii', {
      agent: 'nancii-profile',
      sequences: [
        {
          target: '#general-dialog',
          message: t('NANCII_FIRST_METAL_SONAR'),
          actions: {
            cb: (_, close) => {
              markFirstMetalSonarComplete();
              close();
            },
            closeX: (_, close) => {
              markFirstMetalSonarComplete();
              close();
            },
          },
        },
      ],
    });
  };

  const handleUseMetalSonar = async () => {
    trackAction('use_metal_sonar', {
      circle_size: currentCoinWithBA.value.circle.radius,
      user_crystals: crystals.value,
    });
    markFirstMetalSonarComplete();
    closeUnifyInstructor();
    closeDialog('silver_coin');
    await delay(500);
    await handleRequestCoinSonar(true);
  };

  const handleStandbyMetalSonar = () => {
    openDialog('standby_metal_sonar');
    trackAction('standby_metal_sonar', {
      circle_size: currentCoinWithBA.value.circle.radius,
      user_crystals: crystals.value,
    });
  };

  const handleShare = async () => {
    const res = await tryShare2({
      text: t('SILVER_COIN_SHARE_MESSAGE', {
        NAME: `${coinRef.value.brand_name} ${t('SILVERCOINPOPUP_COIN_1', {
          NUMBER: coinRef.value.coin_number,
        })}`,
        URL: process.env.APP_END_POINT,
      }),
    });
    track('silvercoin_share', { status: res.status });
    if (res.status === 'not_supported') {
      errorNotify({ message: t('SHARE_NOT_SUPPORT') });
    }
  };

  const onClose = (emitClose: () => void) => {
    if (isFreeShrink.value) openDialog('give_up_free_shrink');
    else emitClose();
  };

  const setupFoundCoinClick = (onCloseCallback: () => void) => {
    useClick('goFoundCoin', () => {
      onCloseCallback();
      if (isFreeShrink.value) {
        trackAction('free_circle_chrink_popup', { action: 'found_coin' });
        return;
      }
      trackAction('silvercoin_popup', { action: 'silvercoin_popup_foundcoin' });
      openDialog('enter_serial_number', { fromMap: true });
    });
  };

  const initializeComponent = async () => {
    await nextTick();
    storeUser.fetchUser();

    if (!coinRef.value.canUsePowerUp) {
      errorNotify({
        message: t('MAX_PRIVATE_CIRCLE_SHRINK'),
        timeout: 0,
        actions: [
          {
            handler: () => {
              track('silvercoin_error_smallestcircle', {
                action: 'silvercoin_error_smallestcircle_close',
              });
              closeNotify();
            },
          },
        ],
      });
    }

    if (!firstTimeState.value.isFirstTap) checkWithFeaturedirstTapCoin();
    // if (shouldShowMetalSonarIntro.value) checkWithFirstMetalSonar();
  };

  const cleanup = () => {
    closeNotify();
    closeUnifyInstructor();
  };

  const setupCountdownWatch = () => {
    watch(
      () => powerUpCountdownText.value,
      (newValue) => {
        const el = document.querySelector('#TIME');
        if (el) el.innerHTML = newValue;
      }
    );
  };

  return {
    coinData,
    currentCoinWithBA,
    firstTimeState: readonly(firstTimeState),
    nextShrinkCountdown,
    shrinkPowerUpCountdown,
    powerUpCountdownText,
    canUsePowerUp,
    isSmallestPublicCircle,
    hasPaidCircle,
    timeLeftText,
    hasDiscount,
    originalPrice,
    shrinkPrice,
    canAffordShrink,
    canActiveShrink,
    isTriggerMetalSonar,
    shouldShowMetalSonarIntro,
    discount,
    isFreeShrink,
    crystals,
    selectedPowerUp,
    resourceType,
    handleShrinkCircle,
    handleClickCountDown,
    handleUseMetalSonar,
    handleStandbyMetalSonar,
    handleShare,
    onClose,
    trackAction,
    markFirstTapComplete,
    markFirstMetalSonarComplete,
    setupFoundCoinClick,
    initializeComponent,
    cleanup,
    setupCountdownWatch,
  };
}
