<script setup lang="ts">
interface Props {
  showBack?: boolean;
  showClose?: boolean;
}

withDefaults(defineProps<Props>(), {
  showBack: false,
  showClose: true,
});

interface Emits {
  (e: 'close'): void;
  (e: 'back'): void;
}
const emits = defineEmits<Emits>();
</script>

<template>
  <div class="fullscreen">
    <Button
      v-if="showBack"
      class="absolute top-4 left-4 z-10 bds-back-button"
      variant="secondary"
      flat
      @click="emits('back')"
      shape="square"
      size="small"
    >
      <Icon name="arrow-left" :size="16" />
    </Button>
    <div id="rmi-dialog" class="rmi-dialog">
      <div class="flex-1 w-full overflow-hidden flex justify-center items-end">
        <div
          class="w-full max-h-[75%] min-h-[200px] column items-center relative"
        >
          <Button
            v-if="showClose"
            class="absolute -top-5 right-[28px] z-10"
            flat
            @click="emits('close')"
            shape="square"
            size="small"
          >
            <Icon name="cross" :size="16" />
          </Button>
          <div
            class="rmi-dialog-container w-full flex-1 overflow-hidden relative column items-center"
          >
            <div
              class="w-full flex-1 pt-10 pb-3 px-5 overflow-y-auto dialog-content overflow-x-hidden"
            >
              <slot />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bds-back-button {
  opacity: 0;
  transform: translateX(-20px);
  @keyframes back-button {
    to {
      opacity: 1;
      transform: translateX(0px);
    }
  }
  animation-name: back-button;
  animation-duration: 0.3s;
  animation-delay: 0.3s;
  animation-fill-mode: forwards;
}
.rmi-dialog {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-direction: column;

  &-container {
    background: linear-gradient(#b663e9, #4f46c1);
    clip-path: polygon(
      calc(50% - 81px) 12px,
      calc(50% + 81px) 12px,
      calc(50% + 93px) 0,
      calc(100% - 18px) 0,
      100% 18px,
      100% 100%,
      0 100%,
      0 18px,
      18px 0,
      calc(50% - 93px) 0
    );
    background-repeat: no-repeat;
    box-shadow: none;
    min-height: 60svh;
    max-height: 60svh;
    position: relative;
    padding: 0;
    &::before {
      content: '';
      position: absolute;
      top: 12px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 6px;

      border-radius: 8px;
      background: #b161e7;
      z-index: 10;
    }
    & > div {
      background: linear-gradient(180deg, #25196d 8.09%, #1d4189 33.16%);
      clip-path: polygon(
        calc(50% - 81px) 14px,
        calc(50% + 81px) 14px,
        calc(50% + 93px) 2px,
        calc(100% - 18px) 2px,
        calc(100% - 2px) 19px,
        calc(100% - 2px) calc(100% - 2px),
        2px calc(100% - 2px),
        2px 19px,
        18px 2px,
        calc(50% - 93px) 2px
      );
    }
  }
}
</style>
