<script lang="ts" setup>
import { useGlobalInstructor, useInventory, useTick } from '@composables';
import { useDialogStore, useUserStore } from '@stores';
import { InventoryItem } from '@types';
import { onClickOutside } from '@vueuse/core';

interface InventoryTool {
  id: string;
  icon: string;
  labelKey: string;
  canUse: boolean;
  handler: () => void;
  showBadge: boolean;
}

const storeDialog = useDialogStore();
const storeUser = useUserStore();

const { t } = useI18n();
const { push } = useMicroRoute();
const {
  canUseSilverShrink,
  canUseCoinSonar,
  canUseMetalDetector,
  canUseBeacon,
  isFirstInventoryQuickView,
  itemsQuickView,
  handleViewPowerUpDetail,
  triggerQuickViewOnboarding,
} = useInventory();
const { closeUnifyInstructor } = useGlobalInstructor();
const { now } = useTick();

const quickViewRef = ref<HTMLElement | null>(null);

onClickOutside(quickViewRef, () => {
  storeDialog.showInventoryQuickAccess = false;
});

const isVisible = computed(() => storeDialog.showInventoryQuickAccess);

const inventoryTools = computed<InventoryTool[]>(() => {
  const shrinkItem = itemsQuickView.value.shrink[0];
  const coinSonarItem = itemsQuickView.value.coinSonar[0];
  const metalDetectorItem = itemsQuickView.value.metalDetector[0];
  const beaconItem = itemsQuickView.value.beacon[0];

  const isExpiredWith24h = (item: InventoryItem) => {
    if (!item) return false;
    const timeLeft = +new Date(item.expires_at) - now.value;
    const daysLeft = Math.floor(timeLeft / (24 * 60 * 60 * 1000));
    if (daysLeft < 1) return true;
    return false;
  };

  return [
    {
      id: 'silver-shrink',
      icon: 'silver-shrink',
      labelKey: 'BTN_SILVER_SHRINK',
      canUse: canUseSilverShrink.value,
      handler: () => handleViewPowerUpDetail(shrinkItem),
      showBadge: isExpiredWith24h(shrinkItem),
    },
    {
      id: 'coin-sonar',
      icon: 'metal-sonar',
      labelKey: 'BTN_METAL_SONAR',
      canUse: canUseCoinSonar.value,
      handler: () => handleViewPowerUpDetail(coinSonarItem),
      showBadge: isExpiredWith24h(coinSonarItem),
    },
    {
      id: 'metal-detector',
      icon: 'metal-detector',
      labelKey: 'BTN_METAL_DETECTOR',
      canUse: canUseMetalDetector.value,
      handler: () => handleViewPowerUpDetail(metalDetectorItem),
      showBadge: isExpiredWith24h(metalDetectorItem),
    },
    {
      id: 'beacon',
      icon: 'beacon',
      labelKey: 'BTN_BEACON',
      canUse: canUseBeacon.value,
      handler: () => handleViewPowerUpDetail(beaconItem),
      showBadge: isExpiredWith24h(beaconItem),
    },
  ];
});

function handleClose() {
  storeDialog.showInventoryQuickAccess = false;
}

function handleGoToBag() {
  push('inventory');
  handleClose();
}

function handleToolClick(tool: InventoryTool) {
  if (tool.canUse) tool.handler();
}

function handleInventoryQuickViewChange(value: boolean) {
  if (value && isFirstInventoryQuickView.value) {
    triggerQuickViewOnboarding();
  }
  if (!value && isFirstInventoryQuickView.value) {
    closeUnifyInstructor();
    storeUser.updateOnboarding('first_inventory_quick_view');
  }
}

watch(isVisible, handleInventoryQuickViewChange);
</script>
<template>
  <Transition
    name="inventory-quick-view"
    enter-active-class="inventory-quick-view-enter-active"
    leave-active-class="inventory-quick-view-leave-active"
  >
    <div
      ref="quickViewRef"
      v-if="isVisible"
      class="inventory-quick-view-container"
      :class="{
        '!z-[9999]': isFirstInventoryQuickView,
      }"
    >
      <div class="inventory-quick-view">
        <Button
          class="absolute -top-2 right-2 z-10"
          shape="square"
          size="small"
          @click="handleClose"
        >
          <Icon name="cross" :size="16" />
        </Button>

        <div class="flex items-center gap-1.5 p-1.5">
          <Icon name="bag" />
          <span class="font-bold text-[10px]" v-html="t('INVENTORY_HEADER')" />
        </div>

        <div class="inventory-tools-container">
          <div class="grid grid-cols-4 flex-1">
            <div
              v-for="tool in inventoryTools"
              :key="tool.id"
              class="inventory-tool-button relative"
              :class="{
                'inventory-tool-button--disabled': !tool.canUse,
              }"
              @click="handleToolClick(tool)"
            >
              <Icon :name="tool.icon" class="w-full !max-w-[37px]" />
              <div class="text-xs text-center" v-html="t(tool.labelKey)" />
              <div
                class="bg-[#FF7878] p-0.5 rounded-full flex justify-center items-center absolute right-2"
                v-if="tool.showBadge"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="8"
                  height="9"
                  viewBox="0 0 8 9"
                  fill="none"
                >
                  <path
                    d="M2.03245 0.435547V1.45177H3.04868V1.48226C1.32109 1.72615 0 3.22001 0 5.00857C0 6.96988 1.59548 8.56536 3.55679 8.56536C5.51811 8.56536 7.11359 6.96988 7.11359 5.00857C7.11359 4.55127 7.01196 4.12445 6.85953 3.73828L5.93476 4.12445C6.04655 4.41916 6.09736 4.7037 6.09736 5.01873C6.09736 6.43128 4.96935 7.5593 3.55679 7.5593C2.14424 7.5593 1.01623 6.43128 1.01623 5.01873C1.01623 3.60617 2.14424 2.47816 3.55679 2.47816C3.86166 2.47816 4.15637 2.52897 4.45107 2.64076L4.79659 1.68551C4.56286 1.60421 4.31896 1.56356 4.06491 1.52291V1.46194H5.08113V0.445709H2.03245V0.435547ZM7.11359 1.61437C7.11359 1.61437 3.40436 4.46997 3.21128 4.66305C3.01819 4.8663 3.01819 5.161 3.21128 5.36425C3.40436 5.56749 3.70923 5.56749 3.91247 5.36425C4.11572 5.161 7.12375 1.61437 7.12375 1.61437H7.11359Z"
                    fill="white"
                  />
                </svg>
              </div>
            </div>
          </div>

          <Button
            size="small"
            variant="purple"
            @click="handleGoToBag"
            class="go-to-bag-button"
          >
            <span v-html="t('INVENTORY_GO_TO_BAG')" />
            <Icon name="arrow-long" class="-mb-1 ml-0.5" :size="24" />
          </Button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<style scoped lang="scss">
.inventory-quick-view-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 30px;
  background: linear-gradient(
    180deg,
    rgba(16, 5, 28, 0) 2.14%,
    rgba(16, 5, 28, 0.98) 37.8%
  );
  height: 187px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  z-index: 10;

  .inventory-quick-view {
    background: url('imgs/inventory_quick_view.png') no-repeat center center;
    background-size: 100% 100%;
    height: 127px;
    width: 100%;
    position: relative;
  }
}

.inventory-tools-container {
  height: 83px;
  display: flex;
  align-items: center;
  padding: 0 4px;
  gap: 12px;
  background: linear-gradient(0deg, #122665, #110c56);
  width: calc(100% - 10px);
  margin-left: 5px;
}

.inventory-tool-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  cursor: pointer;
  transition: opacity 0.3s ease;

  &--disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
  }
}

.go-to-bag-button {
  flex-shrink: 0;
}

.inventory-quick-view-enter-active,
.inventory-quick-view-leave-active {
  transition: all 0.3s ease-in-out;
}

.inventory-quick-view-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.inventory-quick-view-leave-to {
  transform: translateY(100%);
  opacity: 0;
}
</style>
