import { defineStore } from 'pinia';
import { MAP, BRAND_ACTION, METAL_SONAR, RMI } from '@repositories';
import { difference, merge, minBy } from 'lodash';
import { point } from '@turf/helpers';
import { useMapHelpers } from '@composables';
import type { Map, GeolocateSuccess } from 'vue3-maplibre-gl';
import type { GeolocateControl } from 'maplibre-gl';
import type {
  TGeocontrolState,
  ISilverCoin,
  ISafetyHint,
  IBrandIcon,
  IGroupedSilverCoins,
  IMetalSonar,
  IMetalSonarPrice,
  IMapGeoHashes,
  ISourceGeoHashes,
  TSourceGeoHashes,
  ISilverCoinProperties,
  IMetalSonarPricesData,
  CircleUpdatedData,
  ICircle,
  RmiOutlet,
} from '@types';
import circle from '@turf/circle';
import distance from '@turf/distance';
import dayjs from 'dayjs';

interface MapState {
  loading: boolean;
  _loading: boolean;
  loadingAssets: boolean;
  mapIns: ComputedRef<Map | null>;
  geoIns: GeolocateControl | null;
  lastLocations: [number, number];
  brandActionPhycial: Record<string, { distance: number }>;
  geoState: TGeocontrolState;
  mapGeoHashes: IMapGeoHashes;
  sourceGeoHashes: Record<TSourceGeoHashes, ISourceGeoHashes>;
  silverCoins: ISilverCoin[];
  safetyHints: ISafetyHint[];
  mapIcons: IBrandIcon[];
  groupedSilverCoins: IGroupedSilverCoins;
  zoom: number;
  _zoom: number;
  newCoinDrop: ISilverCoin[];
  coinSonarLayers: IMetalSonar[];
  metalSonarPrices: IMetalSonarPrice[];
  coinSonarPricesData: IMetalSonarPricesData | undefined;
  coinSonar: {
    dataPrices: IMetalSonarPricesData | undefined;
    selectedCircle: ISilverCoinProperties | undefined;
    selectedPrice: IMetalSonarPrice | undefined;
  };
  fromGrids: boolean;
  currentMetalSonar: IMetalSonarPrice | null;
  hideSonarHighlight: boolean;
  selectedSonarCircle: ISilverCoinProperties | null;
  stepsWalking: number;
  directionWalking: string;
  headingDegrees: number;
  rmiOutlets: RmiOutlet[];
  devTools: {
    fakeGps: boolean;
    pickLocationMode: boolean;
    location: [number, number];
    realLocation: [number, number];
    isEnableGPS: boolean;
    targetLocation: [number, number];
    dailyRewardMode: boolean;
  };
}

export const useMapStore = defineStore('map', {
  state: (): MapState => ({
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    mapIns: null,
    geoIns: null,
    loading: true,
    _loading: true,
    loadingAssets: false,
    lastLocations: [0, 0],
    geoState: 'OFF',
    mapGeoHashes: {
      golden: [],
      eliminated: [],
      paidEliminated: [],
    },
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    sourceGeoHashes: {},
    silverCoins: [],
    safetyHints: [],
    mapIcons: [],
    groupedSilverCoins: {
      ongoing: [],
      ongoing_paid: [],
      ongoing_free: [],
      verifying: [],
      found: [],
      past_found: [],
      forfeited: [],
      time_up: [],
    },
    zoom: 0,
    _zoom: 0,
    brandActionPhycial: {},
    newCoinDrop: [],
    coinSonarLayers: [],
    metalSonarPrices: [],
    metalSonarPricesData: null,
    fromGrids: false,
    currentMetalSonar: null,
    selectedSonarCircle: null,
    hideSonarHighlight: false,
    stepsWalking: 0,
    directionWalking: '',
    headingDegrees: 0,
    coinSonar: {
      dataPrices: undefined,
      selectedCircle: undefined,
      selectedPrice: undefined,
    },
    rmiOutlets: [],
    devTools: {
      fakeGps: false,
      location: [0, 0],
      realLocation: [0, 0],
      isEnableGPS: false,
      pickLocationMode: false,
      targetLocation: [0, 0],
      dailyRewardMode: false,
    },
  }),

  getters: {
    isLastLocations(state) {
      return state.lastLocations.every(Boolean);
    },
    totalCoin(state) {
      return state.silverCoins.filter(
        (s) => s.status === 'ongoing' && !s.time_up
      ).length;
    },
    totalSentosaCoin(state) {
      return state.silverCoins.filter(
        (s) =>
          s.status === 'ongoing' &&
          !s.time_up &&
          s.brand_unique_id === 'sentosa'
      ).length;
    },
    totalScienceParkCoin(state) {
      return state.silverCoins.filter(
        (s) =>
          s.status === 'ongoing' &&
          !s.time_up &&
          s.brand_unique_id === 'capitaland_science_park'
      ).length;
    },
    silverCoinById(state) {
      return state.silverCoins.reduce<Record<string, ISilverCoin>>((r, a) => {
        r[a._id] = a;
        return r;
      }, {});
    },
  },

  actions: {
    setGeoIns(geo: GeolocateControl) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      this.geoIns = geo;
    },

    setGeoState(tt: GeolocateSuccess) {
      const { _watchState } = tt.target;
      this.geoState = _watchState as TGeocontrolState;
      if (this.geoState === 'OFF') LocalStorage.remove('enable_gps');
    },

    async triggerGPS() {
      if (!this.geoIns || !this.mapIns) return;
      this.geoIns.trigger();
    },

    setUserLocation([lng, lat]: [number, number]) {
      if (process.env.IS_TESTING_ENV && this.devTools.fakeGps) {
        this.devTools.realLocation = [lng, lat];
        return;
      }
      this.lastLocations = [lng, lat];
    },

    setZoom() {
      if (!this.mapIns) return;
      this._zoom = this.mapIns.getZoom();
      this.zoom = this.mapIns.getZoom();
    },

    async fetchMapGeoHashes() {
      try {
        const [golden, eliminated] = await Promise.all([
          MAP.getGeoHashes(),
          MAP.getEliminatedGeoHashes(),
        ]);

        this.mapGeoHashes = {
          golden:
            difference(
              golden.data,
              eliminated.data.free,
              eliminated.data.paid
            ) || [],
          eliminated:
            difference(eliminated.data.free, eliminated.data.paid) || [],
          paidEliminated: eliminated.data.paid,
        };

        const sources = this.makeSourceFromMapGeoHashes(this.mapGeoHashes);
        this.sourceGeoHashes = sources;
      } catch (error) {
        // console.error('error', error);
      }
    },

    makeSourceFromMapGeoHashes(
      geohashes: IMapGeoHashes,
      properties = {}
    ): Record<TSourceGeoHashes, ISourceGeoHashes> {
      const { decodeBbox, polyMask } = useMapHelpers();

      const sources: any = {};

      const data = Object.entries(geohashes) as [TSourceGeoHashes, string[]][];

      for (const [type, geos] of data) {
        sources[type] = {
          type: 'FeatureCollection',
          features: geos.map((geohash: string) => {
            const bbox = decodeBbox(geohash);
            const poly = polyMask(bbox, properties);
            poly.properties = {
              ...properties,
              geohash,
              circle: {},
              winner_info: {},
              videos: [],
              radius: 0,
            };

            if (type === 'paidEliminated') {
              const coordinates = poly.geometry.coordinates[0];
              poly.geometry.coordinates[0] = [
                ...coordinates,
                coordinates[1],
                coordinates[3],
              ];
            }

            return poly;
          }),
        };
      }

      return sources;
    },

    addFreeEliminated(geohashes: string[]) {
      this.mapGeoHashes.eliminated.push(...geohashes);
      this.mapGeoHashes.golden = difference(
        this.mapGeoHashes.golden,
        this.mapGeoHashes.eliminated,
        this.mapGeoHashes.paidEliminated
      );

      const sources = this.makeSourceFromMapGeoHashes(this.mapGeoHashes);
      this.sourceGeoHashes = sources;
    },

    addPaidEliminated(geohashes: string[]) {
      this.mapGeoHashes.paidEliminated.push(...geohashes);
      this.mapGeoHashes.eliminated = difference(
        this.mapGeoHashes.eliminated,
        this.mapGeoHashes.paidEliminated
      );
      this.mapGeoHashes.golden = difference(
        this.mapGeoHashes.golden,
        this.mapGeoHashes.eliminated,
        this.mapGeoHashes.paidEliminated
      );
    },

    async fetchGeohash() {
      try {
        const { data } = await MAP.getGeoHashes();
        this.mapGeoHashes.golden = difference(
          data,
          this.mapGeoHashes.eliminated
        );
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchEliminatedGeohash() {
      try {
        const { data } = await MAP.getEliminatedGeoHashes();
        this.mapGeoHashes.eliminated = difference(data.free, data.paid);
        this.mapGeoHashes.paidEliminated = data.paid;
      } catch (error) {
        // console.error('error', error);
      }
    },

    setCoinDrop(coins: ISilverCoin[]) {
      this.newCoinDrop = coins;
    },

    createCircle(coin: ISilverCoin) {
      const { center, radius } = coin.circle;
      return circle([center.lng, center.lat], radius, {
        properties: {
          ...coin,
          radius,
        },
        units: 'meters',
      });
    },

    processCoin(coin: ISilverCoin, circleType: keyof ISilverCoin) {
      coin.circle = coin[circleType] as ICircle;
      coin.winner_info = coin.winner_info || {};
      coin.videos = coin.videos || [];
      return this.createCircle(coin);
    },

    setGroupedSilverCoins(silverCoins: ISilverCoin[]) {
      const initial = {
        ongoing: [],
        ongoing_paid: [],
        ongoing_free: [],
        verifying: [],
        found: [],
        past_found: [],
        forfeited: [],
        time_up: [],
      } as IGroupedSilverCoins;

      if (!silverCoins.length) {
        this.groupedSilverCoins = initial;
        return;
      }

      const grouped = silverCoins.reduce((acc, coin) => {
        switch (coin.status) {
          case 'found':
            const { lat, lng } = coin.location;
            const past24Hours = dayjs().diff(dayjs(coin.found_at), 'hour') > 24;

            if (past24Hours)
              acc.past_found.push(
                point([lng, lat], {
                  ...coin,
                  circle: {
                    ...coin.lastCircle,
                    center: coin.location,
                  },
                  icon: 'silver-flag-2',
                  iconSizeMax: 0.5,
                  iconSizeMin: 0.2,
                })
              );
            else
              acc.found.push(
                point([lng, lat], {
                  ...coin,
                  circle: {
                    ...coin.lastCircle,
                    center: coin.location,
                  },
                  icon: 'silver-flag-2',
                  iconSizeMax: 0.5,
                  iconSizeMin: 0.2,
                })
              );
            break;
          case 'ongoing':
            if (!coin.time_up) {
              if (coin.paidCircle?.center)
                acc.ongoing_paid.push(this.processCoin(coin, 'paidCircle'));
              acc.ongoing_free.push(this.processCoin(coin, 'freeCircle'));
            } else acc.time_up.push(this.processCoin(coin, 'freeCircle'));
            acc.ongoing.push(this.processCoin(coin, 'freeCircle'));
            break;
          case 'verifying':
            acc.verifying.push(this.processCoin(coin, 'verifyingCircle'));
            break;
          case 'forfeited':
            coin.circle = coin.lastCircle || coin.circle;
            acc.forfeited.push(this.processCoin(coin, 'circle'));
            break;
        }

        return acc;
      }, initial);

      this.groupedSilverCoins = grouped;
    },

    async fetchSilverCoin() {
      try {
        const { data } = await MAP.getSilverCoin();
        this.silverCoins = data;
        this.setGroupedSilverCoins(data);
      } catch (error) {
        // console.error('error', error);
      }
    },
    async fetchBrandActionPhycical() {
      if (!this.lastLocations.length) return;
      try {
        const res = (await BRAND_ACTION.getPhysical({
          lng: this.lastLocations[0],
          lat: this.lastLocations[1],
        })) as { data: any };
        const physicals = res.data.reduce((obj: any, item: any) => {
          if (item.brand_action && !obj[item.brand_action])
            obj[item.brand_action] = item;
          return obj;
        }, {});
        this.brandActionPhycial = physicals;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchBrandIcon() {
      try {
        const { data } = await BRAND_ACTION.getBrandIcons();
        this.mapIcons = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchMetalSonar() {
      try {
        const { data } = await METAL_SONAR.list();
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const uniqueDataMap: Map<string, IMetalSonar> = new Map();

        for (const item of data) {
          const key = `${item.location.lat}-${item.location.lng}-${item.radius}`;
          if (!uniqueDataMap.has(key)) uniqueDataMap.set(key, item);
        }

        this.coinSonarLayers = Array.from(uniqueDataMap.values());
      } catch (error) {
        console.error('error', error);
      }
    },

    setSonarPricesData(data: IMetalSonarPricesData) {
      this.coinSonarPricesData = data;
      this.metalSonarPrices = data.prices;
      if (!this.currentMetalSonar) this.currentMetalSonar = data.prices[0];
    },

    getNearestOutlet(brand_unique_id: string, sv_client?: string) {
      const nearestOutlet = minBy(
        this.mapIcons
          .filter(
            (icon) =>
              icon.brand_unique_id === brand_unique_id &&
              (!sv_client || icon.display.sv_client === sv_client)
          )
          .map((icon) => ({
            ...icon,
            distance: distance(
              this.lastLocations,
              [icon.location.lng, icon.location.lat],
              { units: 'meters' }
            ),
          })),
        'distance'
      );
      return nearestOutlet;
    },

    async fetchNewSilverCoin(id: string) {
      try {
        const { data } = await MAP.retrieveCoin(id);
        this.silverCoins.push(data.coin);
        this.groupedSilverCoins.ongoing_free.push(
          this.processCoin(data.coin, 'freeCircle')
        );
        return true;
      } catch (error) {
        return false;
      }
    },

    async updateCircle(data: CircleUpdatedData) {
      const targetCoinIndex = this.silverCoins.findIndex(
        (coin) => coin._id === data._id
      );
      if (targetCoinIndex === -1) return this.fetchNewSilverCoin(data._id);

      const coin = this.silverCoins[targetCoinIndex];
      if (coin.status !== 'ongoing') return false;

      const targetCircleIndex = this.groupedSilverCoins.ongoing_free.findIndex(
        (circle) => circle.properties._id === data._id
      );
      if (targetCircleIndex === -1) {
        console.log('need handle new circle', data._id);
        return false;
      }

      this.silverCoins = [
        ...this.silverCoins.filter((coin) => coin._id !== data._id),
        merge({}, coin, data),
      ];
      this.groupedSilverCoins.ongoing_free = [
        ...this.groupedSilverCoins.ongoing_free.filter(
          (circle) => circle.properties._id !== data._id
        ),
        this.processCoin(merge({}, coin, data), 'freeCircle'),
      ];

      const paidIndex = this.groupedSilverCoins.ongoing_paid.findIndex(
        (circle) => circle.properties._id === data._id
      );
      if (paidIndex !== -1) {
        const properties =
          this.groupedSilverCoins.ongoing_paid[paidIndex].properties;
        const radius = properties.paidCircle.radius;
        if (radius >= data.freeCircle.radius) {
          this.groupedSilverCoins.ongoing_paid =
            this.groupedSilverCoins.ongoing_paid.filter(
              (circle) => circle.properties._id !== data._id
            );
        }
      }

      return true;
    },

    async updateCoinStatus(data: CircleUpdatedData) {
      const targetCoinIndex = this.silverCoins.findIndex(
        (coin) => coin._id === data._id
      );
      if (targetCoinIndex === -1) return false;

      const coinData = await MAP.retrieveCoin(data._id);
      this.silverCoins = [
        ...this.silverCoins.filter((coin) => coin._id !== data._id),
        coinData.data.coin,
      ];
      this.setGroupedSilverCoins(this.silverCoins);

      return true;
    },

    async fetchRmiOutlets(lng: number, lat: number) {
      try {
        const { data } = await RMI.get({ lat, lng });
        this.rmiOutlets = data;
      } catch (error) {
        // console.error('error', error);
      }
    },
  },
});
