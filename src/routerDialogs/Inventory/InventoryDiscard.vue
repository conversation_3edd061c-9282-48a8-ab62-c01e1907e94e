<script lang="ts" setup>
import { useInventory } from '@composables';
import { InventoryItem } from '@types';

interface Props {
  inventoryItem: InventoryItem;
}

const props = defineProps<Props>();

const { t } = useI18n();
const { closeDialog, openDialog } = useMicroRoute();
const { itemAssetNames, itemDisplayNames, MAX_STACK_SIZE } = useInventory();

const quantity = ref(1);

function handleClose() {
  closeDialog('inventory_discard');
  openDialog('inventory_detail', { inventoryItem: props.inventoryItem });
}

function handleNext() {
  closeDialog('inventory_discard');
  openDialog('inventory_confirm_discard', {
    inventoryItem: props.inventoryItem,
    quantity: quantity.value,
  });
}
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div
        v-html="
          t('INVENTORY_DISCARD_HEADER', {
            NAME: itemDisplayNames[inventoryItem.item_type],
          })
        "
      ></div>
    </template>
    <div class="text-center px-5 relative">
      <Icon
        :name="itemAssetNames[inventoryItem.item_type]"
        :size="97"
        class="mx-auto mb-3"
      />
      <div
        class="rounded-[6px] w-max mx-auto py-2 px-4 bg-[#09090980] mb-5"
        :class="{
          'max-stack': inventoryItem.quantity >= MAX_STACK_SIZE,
        }"
        v-html="
          t('INVENTORY_DETAIL_OWNED', {
            QUANTITY: inventoryItem.quantity,
            MAX: MAX_STACK_SIZE,
          })
        "
      ></div>
      <div class="mb-2" v-html="t('INVENTORY_DISCARD_DESC')"></div>
      <div
        class="bg-[#091A3B] rounded p-4 flex justify-around items-center mb-5"
      >
        <div
          class="circle"
          :class="{
            'opacity-30 pointer-events-none': quantity === 1,
          }"
          @click="quantity--"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
          >
            <path
              d="M18.4853 10.0009H1.51472"
              stroke="black"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <div class="text-2xl font-bold">{{ quantity }}</div>
        <div
          class="circle"
          :class="{
            'opacity-30 pointer-events-none':
              quantity >= inventoryItem.quantity,
          }"
          @click="quantity++"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
          >
            <path
              d="M10 18.4862V1.51562"
              stroke="black"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M18.4853 10.0009H1.51472"
              stroke="black"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>

      <div class="flex items-center gap-4 w-full flex-nowrap">
        <Button
          variant="purple"
          :label="t('INVENTORY_DISCARD_BTN_CANCEL')"
          @click="handleClose"
        />
        <Button :label="t('INVENTORY_DISCARD_BTN_NEXT')" @click="handleNext" />
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.max-stack {
  background: linear-gradient(
    180deg,
    rgba(197, 54, 54, 0.64) 0%,
    rgba(215, 47, 47, 0.8) 65.22%
  );
}

.circle {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: linear-gradient(180deg, #99e3ed 0%, #6bcfdd 100%);
}
</style>
