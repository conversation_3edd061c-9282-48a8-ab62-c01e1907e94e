<script lang="ts" setup>
import { useSound, useTrackData } from '@composables';
import { cn } from '@helpers';
import { clamp } from 'lodash';

const { openDialog, closeDialog } = useMicroRoute();
const { play, stop } = useSound();
const { track } = useTrackData();

const videoRef = ref<HTMLVideoElement | null>(null);
const loading = ref(true);
const muted = ref(false);

function handleEnded() {
  closeDialog('video_intro');
  openDialog('onboarding_golden');
  play('default');
}

function handleClickVideo() {
  if (!videoRef.value || !muted.value) return;
  muted.value = false;
  videoRef.value.muted = false;
  videoRef.value.volume = 0;
  easeInVolume();
}

onMounted(() => {
  track('onboarding_video');
  stop();
});

function easeInVolume() {
  let time = 0;
  const duration = 2000;
  const step = 20;
  const interval = setInterval(() => {
    if (time >= duration || !videoRef.value) {
      clearInterval(interval);
      return;
    }

    // ease in out
    const t = time / duration;
    videoRef.value.volume = clamp(
      t <= duration / 2 ? 2 * t ** 2 : 0.5 + 2 * t * (1 - t),
      0,
      1
    );

    time += step;
  }, step);
}

watch(loading, async (value) => {
  if (!videoRef.value || value) return;
  try {
    await videoRef.value.play();
    videoRef.value.muted = false;
    if (videoRef.value.paused)
      throw new DOMException('Video is paused! Safari?', 'NotAllowedError');
    easeInVolume();
  } catch (e) {
    const error = e as DOMException;
    console.log(error);
    if (error.name === 'NotAllowedError') {
      muted.value = true;
      videoRef.value.muted = true;
      videoRef.value.play();
    }
  }
});
</script>
<template>
  <div class="bg-black fullscreen" @click="handleClickVideo">
    <video
      ref="videoRef"
      class="relative object-cover w-full h-full"
      autoplay
      muted
      playsinline
      :controls="false"
      src="https://htm-global-production.s3.ap-southeast-1.amazonaws.com/assets/htm1m_intro.mp4"
      preload="metadata"
      @canplay="loading = false"
      @ended="handleEnded"
    />
    <div class="z-10 flex items-center justify-center fullscreen">
      <q-spinner color="ffffff" size="5em" v-if="loading" />
    </div>
    <Button
      class="absolute top-2 right-2 z-[99]"
      shape="square"
      @click="
        track('skip_video');
        handleEnded();
      "
    >
      <Icon name="skip" :size="12" />
    </Button>
    <div
      :class="
        cn(
          'fixed flex items-center justify-center p-2 rounded-full bottom-2 right-2 bg-slate-500/20 backdrop-blur-[2px] pointer-events-none transition-opacity opacity-0',
          muted && 'opacity-100'
        )
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1.5em"
        height="1.5em"
        viewBox="0 0 16 16"
      >
        <path
          fill="none"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
          d="M1.75 5.75v4.5h2.5l4 3V2.75l-4 3zm12.5 0l-3.5 4.5m0-4.5l3.5 4.5"
        />
      </svg>
    </div>
  </div>
</template>
