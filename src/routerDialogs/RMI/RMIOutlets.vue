<script setup lang="ts">
import { DailyMissionItem } from '@components';
import BrandActionItem from '@components/Offerwalls/BrandActionItem.vue';
import { IBrandAction, IBrandHook, IDailyMission, RmiAds } from '@types';

interface Props {
  brandActions: IBrandAction[];
  rmiAds: RmiAds[];
  dailyMissions: IDailyMission[];
  brandHooks: IBrandHook;
}

defineProps<Props>();

const { closeDialog } = useMicroRoute();
const { t } = useI18n();
</script>

<template>
  <DbsDialog @close="closeDialog('rmi_outlets')">
    <div class="text-sm mb-5" v-html="t('RMI_DESC_1')"></div>
    <template v-if="brandActions.length">
      <BrandActionItem
        v-for="item in brandActions"
        :key="`brand-action-${item._id}`"
        :data="item"
        :brandHooks="brandHooks"
      />
    </template>
    <template v-if="rmiAds.length">
      <div
        class="mb-4 !mt-[30px] text-left dbs_brand_action_item"
        v-for="item in rmiAds"
        :key="`dbs-brand-${item}`"
      >
        <div class="flex items-center">
          <div style="flex: 1" class="justify-center gap-2 column">
            <div class="text-base font-medium" v-html="t(item.title)"></div>
            <div
              class="text-sm italic font-normal"
              v-html="t(item.description)"
            ></div>
          </div>
          <a :href="item.link" target="_blank" rel="noopener noreferrer">
            <Button
              size="small"
              :label="t('OFFERWALL_BASE_BRANDACTIONACTIONBUTTON')"
            />
          </a>
        </div>
      </div>
    </template>

    <template v-if="dailyMissions.length">
      <DailyMissionItem
        class="mb-4"
        :class="{
          'mt-[30px]': index === 0,
        }"
        v-for="(mission, index) in dailyMissions"
        :key="mission.unique_id"
        :mission="mission"
      />
    </template>
  </DbsDialog>
</template>
<style lang="scss" scoped>
.dbs_brand_action_item {
  background: linear-gradient(0deg, #320b5b, #320b5b), #51178c;
  border: 2px solid #51178c;
  background-size: 100% 100% !important;
  width: 100%;
  padding: 10px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    width: 80%;
    height: 12px;
    left: -5px;
    transform: skew(25deg);
    bottom: -14px;
    background: linear-gradient(180deg, #804ebe 0%, #5a2998 100%);
    border-radius: 2px;
  }
  &::after {
    content: '';
    position: absolute;
    width: 80%;
    height: 12px;
    right: -5px;
    transform: skew(-25deg);
    bottom: -14px;
    background: linear-gradient(180deg, #804ebe 0%, #5a2998 100%);
    border-radius: 2px;
  }
}
</style>
