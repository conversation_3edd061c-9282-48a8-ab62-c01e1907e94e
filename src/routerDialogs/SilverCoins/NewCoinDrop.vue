<script setup lang="ts">
import { useMapStore, useUserStore } from '@stores';
import { useAsync, useGlobal } from '@composables';
import { USER } from '@repositories';
import { LngLatBoundsLike, useFitBounds } from 'vue3-maplibre-gl';
import type { ISilverCoin } from '@types';
import turfDistance from '@turf/distance';
import circle from '@turf/circle';
import bbox from '@turf/bbox';

interface Emits {
  (e: 'close'): void;
}

interface Props {
  coins: ISilverCoin[];
}

const props = defineProps<Props>();

const emits = defineEmits<Emits>();

const storeMap = useMapStore();
const storeUser = useUserStore();

const { lastLocations } = storeToRefs(storeMap);
const { t } = useI18n();
const { checkNewCoinDrop } = useGlobal();
const { setFitBounds } = useFitBounds({
  map: storeMap.mapIns,
});

function distance(coin: ISilverCoin) {
  const { center, radius } = coin.circle;
  return (
    Math.max(
      0,
      turfDistance(lastLocations.value, [center.lng, center.lat], {
        units: 'meters',
      }) - radius
    ) / 1000
  ).toFixed(2);
}

const { execute: onClose } = useAsync({
  async fn() {
    const ids = props.coins.map((item) => item._id);
    USER.updateDropCoin(ids);
    storeUser.fetchUser();
    checkNewCoinDrop();
    emits('close');
  },
});

async function goToCoin(coin: ISilverCoin) {
  const { center, radius } = coin.circle;
  const _circle = circle([center.lng, center.lat], radius * 1.1, {
    units: 'meters',
  });
  const box = bbox(_circle) as LngLatBoundsLike;
  await setFitBounds(box, {
    duration: 2000,
    animate: true,
  });
  onClose();
}
</script>
<template>
  <Dialog @close="onClose">
    <template #header>
      <div v-html="t('NEWCOIN_POPUP_HEADING')"></div>
    </template>
    <div class="flex flex-col gap-4">
      <div
        class="flex items-center justify-between bg-[#091a3c] rounded p-3"
        v-for="c in coins"
        :key="c._id"
        @click="goToCoin(c)"
      >
        <div class="flex flex-col">
          <div class="flex items-center" v-if="lastLocations.length">
            <Icon name="icons/distant" :size="6.5" />
            <span>~{{ distance(c) }} km</span>
          </div>
          <div class="text-base font-bold">
            {{
              `${c.brand_name} ${t('SILVERCOINPOPUP_COIN_1', {
                NUMBER: c.coin_number,
              })}`
            }}
          </div>
          <div v-html="t('NEWCOIN_POPUP_DESC', { REWARD: c.reward })"></div>
        </div>
        <Icon name="arrow-left" class="rotate-180" :size="16" />
      </div>
    </div>
  </Dialog>
</template>
