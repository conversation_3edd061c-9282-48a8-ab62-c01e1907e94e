<script lang="ts" setup>
import {
  dateTimeFormat,
  FULL_DATE_MONTH_TIME_24H_FORMAT,
  numeralFormat,
} from '@helpers';
import { useVouchersStore } from '@stores';
import type { IPaymentResult } from '@types';

interface Props {
  status: 'success' | 'failed' | 'processing';
  data?: IPaymentResult;
}

const props = defineProps<Props>();

const storeVouchers = useVouchersStore();
const { user } = storeToRefs(storeVouchers);
const { closeAllDialog, push } = useMicroRoute();
const { t } = useI18n();

const status = ref<'success' | 'failed' | 'processing'>(props.status);
const timeRemaning = ref(5);

onMounted(async () => {
  await nextTick();
  const interval = setInterval(() => {
    timeRemaning.value -= 1;
    if (timeRemaning.value === 0) {
      if (!!props.data) status.value = 'success';
      else status.value = 'failed';
      clearInterval(interval);
    }
  }, 1000);
});
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div
        v-html="
          status === 'processing'
            ? t('PAYMENT_RESULT_HEADER_1')
            : status === 'success'
            ? t('PAYMENT_RESULT_HEADER_2')
            : t('PAYMENT_RESULT_HEADER_3')
        "
      ></div>
    </template>
    <template v-if="status === 'processing'">
      <Icon class="!w-full rounded-lg mb-5" name="sqkii_voucher_kv" />
      <div
        class="text-base text-center"
        v-html="t('PAYMENT_RESULT_DESC_1')"
      ></div>
    </template>
    <template v-else-if="status === 'success' && !!data">
      <div class="silver-coin">
        <Icon class="mt-10" name="top-up-success" :size="140" />
      </div>
      <div class="banner mb-5">
        <div class="text-3xl font-bold">
          {{ user?.currency || 'S$' }}
          {{ numeralFormat(Number(data.amount), '0,0.00') }}
        </div>
        <div class="text-sm" v-html="'of Sqkii Vouchers used at'"></div>
        <div class="text-balance font-bold" v-html="data.outlet_name"></div>
      </div>
      <div class="text-center">
        <div class="text-lg font-bold text-[#00E0FF]">
          {{
            dateTimeFormat(data.completed_at, FULL_DATE_MONTH_TIME_24H_FORMAT)
          }}
        </div>
        <div
          class="text-base opacity-50 mb-8"
          v-html="t('PAYMENT_RESULT_DESC_2', { ID: data.payment_id })"
        ></div>
        <div class="flex flex-nowrap items-start gap-2 mb-5">
          <Icon name="alert-circle" class="mt-2" />
          <div
            class="text-sm text-left"
            v-html="t('PAYMENT_RESULT_DESC_3')"
          ></div>
        </div>
        <Button
          :label="t('PAYMENT_RESULT_BTN_1')"
          class="w-full"
          @click="closeAllDialog"
        />
      </div>
    </template>
    <template v-else>
      <div class="silver-coin">
        <Icon class="mt-10" name="top-up-failed" :size="140" />
      </div>
      <div
        class="text-sm text-center mb-5 px-5 -mt-10"
        v-html="t('PAYMENT_RESULT_DESC_4')"
      ></div>
      <div class="flex items-center flex-nowrap gap-5">
        <Button
          :label="t('PAYMENT_RESULT_BTN_2')"
          variant="purple"
          size="max-content"
          @click="
            push(-1);
            closeAllDialog();
          "
          class="flex-1"
        />
        <Button
          :label="t('PAYMENT_RESULT_BTN_3')"
          @click="closeAllDialog"
          size="max-content"
          class="flex-1"
        />
      </div>
    </template>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/big-glow-2.png');
  background-size: cover;
  background-position: center;
  margin-left: -30px;
  margin-top: -50px;
}

.banner {
  margin-top: -100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2px;
  width: 76vw;
  height: 33vw;
  background-image: url('/imgs/shrink-circle-banner.png');
  background-size: 100% 100%;
  padding: 10px;
}
</style>
