<script lang="ts" setup>
import { useUserStore } from '@stores';
import { useClick } from '@composables';

const storeUser = useUserStore();

const { seasonCode } = storeToRefs(storeUser);
const { t } = useI18n();
const { push, openDialog } = useMicroRoute();

useClick('goTAC', () => {
  openDialog('tac');
});

const CONTENTS = [
  {
    header: t('ENSURINGFAIRNESS_1_TITLE'),
    content: t('ENSURINGFAIRNESS_1_BODY'),
  },
  {
    header: t('ENSURINGFAIRNESS_2_TITLE'),
    content: t('ENSURINGFAIRNESS_2_BODY'),
  },
  {
    header: t('ENSURINGFAIRNESS_3_TITLE'),
    content: t('ENSURINGFAIRNESS_3_BODY'),
  },
  {
    header: t('ENSURINGFAIRNESS_4_TITLE'),
    content: t('ENSURINGFAIRNESS_4_BODY'),
  },
];
</script>

<template>
  <div
    class="fit ensuring-fairness"
    :class="`season-${seasonCode.toLowerCase()}`"
  >
    <div class="absolute -top-[13vh] left-0 w-full h-full">
      <div class="ensuring-fairness__bg"></div>
    </div>
    <Button
      class="fixed top-2 left-2 z-50"
      shape="square"
      variant="secondary"
      @click="push(-1)"
    >
      <Icon name="arrow-left" />
    </Button>
    <div
      class="header fixed font-bold text-lg mt-2 z-50 text-border"
      v-html="t('MENU_FAIRNESS_HEADER')"
    ></div>

    <div class="ensuring-fairness__content">
      <div
        class="text-center italic mb-[30px] mt-5 px-5"
        v-html="t('ENSURINGFAIRNESS_DESC')"
      ></div>
      <Expansion
        v-for="content in CONTENTS"
        :key="content.header"
        group="ensuring"
      >
        <template v-slot:header>
          <div class="text-xl font-bold w-[90%]" v-html="content.header"></div>
        </template>
        <q-card style="background: transparent">
          <q-card-section>
            <div class="text-sm" v-html="content.content"></div>
          </q-card-section>
        </q-card>
      </Expansion>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.ensuring-fairness {
  display: flex;
  flex-flow: column;
  // padding: 100% 10px 20px;
  padding-top: 85%;
  background-color: #0f132a !important;

  // &.season-sg {
  //   background: url('/imgs/kv/sg.png') center -10vw no-repeat;
  //   background-size: 100% 112vw;
  // }
  // &.season-vn {
  //   background-size: 100% 112vw !important;
  //   background: url('/imgs/kv/vn.png') center -10vw no-repeat;
  // }

  &__bg {
    background-image: url(/imgs/capitaland-kv/ensuring-fairness-bg.png);
    width: 100%;
    height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }

  &__content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #0f132a !important;
    z-index: 9999;
  }

  .header {
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
  }
  .text-border {
    text-shadow: rgb(65, 6, 60) 2px 0px 0px,
      rgb(65, 6, 60) 1.75517px 0.958851px 0px,
      rgb(65, 6, 60) 1.0806px 1.68294px 0px,
      rgb(65, 6, 60) 0.141474px 1.99499px 0px,
      rgb(65, 6, 60) -0.832294px 1.81859px 0px,
      rgb(65, 6, 60) -1.60229px 1.19694px 0px,
      rgb(65, 6, 60) -1.97998px 0.28224px 0px,
      rgb(65, 6, 60) -1.87291px -0.701566px 0px,
      rgb(65, 6, 60) -1.30729px -1.5136px 0px,
      rgb(65, 6, 60) -0.421592px -1.95506px 0px,
      rgb(65, 6, 60) 0.567324px -1.91785px 0px,
      rgb(65, 6, 60) 1.41734px -1.41108px 0px,
      rgb(65, 6, 60) 1.92034px -0.558831px 0px;
  }
}
</style>
