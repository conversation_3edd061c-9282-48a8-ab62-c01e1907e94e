<script setup lang="ts">
import { successNotify } from '@helpers';
import { useUserStore } from '@stores';
import { copyToClipboard } from 'quasar';
import { useAsync, useGlobal } from '@composables';
import { REFERRAL } from '@repositories';

const storeUser = useUserStore();

const { user, seasonCode, settings, referrals } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog, push } = useMicroRoute();
const { isLogged } = useGlobal();

const URL = computed(
  () => process.env.APP_END_POINT?.replace(/^https?:\/\//, '') || ''
);

const referLink = computed(
  () => `${URL.value}?ref=${user.value?.referral_code}`
);

const SOCIALS = computed(() => [
  {
    network: 'facebook',
    icon: 'fb',
    sharelink: `https://www.facebook.com/sharer/sharer.php?u=${referLink.value}`,
    link: 'https://www.facebook.com/sqkii',
  },
  {
    network: 'instagram',
    icon: 'insta',
    link: 'https://www.instagram.com/sqkiimouse',
  },
  {
    network: 'Telegram',
    icon: 'telegram_w',
    sharelink: `https://t.me/share/url?url=${referLink.value}&text=${t(
      'REFERRAL_DESC_COPIED',
      { URL: referLink.value }
    )}`,
    link: `https://t.me/share/url?url=${referLink.value}`,
  },
  {
    network: 'whatsapp',
    icon: 'wa',
    link: '',
    sharelink: `https://api.whatsapp.com/send?text=${t('REFERRAL_DESC_COPIED', {
      URL: referLink.value,
    })}`,
  },
]);

function handleCopy() {
  const text = t('REFERRAL_DESC_COPIED', { URL: referLink.value });

  copyToClipboard(text);
  successNotify({
    message: t('REFERRAL_COPIED'),
  });
}

const { loading, execute: handlClaim } = useAsync({
  async fn(id: string) {
    await REFERRAL.claimReferral({ id });
    await storeUser.fetchReferral();
    await storeUser.fetchUser();
  },
});

async function handleSignUp() {
  if (!user.value?.mobile_number) {
    openDialog('signup');
    return;
  }

  if (!user.value?.verified_mobile_number_at) {
    await storeUser.fetchSetting();
    const remainingQuota = settings.value?.zalo_daily_quota.remainingQuota;
    if (Number(remainingQuota) <= 0) {
      push('offer_wall');
      return;
    }
    openDialog('otp');
  }
}

onMounted(async () => {
  await storeUser.fetchReferral();
});
</script>

<template>
  <div class="fit referral" :class="`season-${seasonCode.toLowerCase()}`">
    <div
      class="fixed top-0 left-0 flex justify-center items-center w-full h-20 z-50"
    >
      <Button
        class="absolute top-2 left-2"
        shape="square"
        variant="secondary"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-bold text-border"
        v-html="t('REFERRAL_TITLE')"
      ></div>
      <div class="absolute top-3 right-3">
        <HeaderCrystal />
      </div>
    </div>
    <div class="absolute -top-[13vh] left-0 w-full h-full">
      <div class="referral__bg"></div>
    </div>
    <!-- <CarAnimation assets="/blocker/new_car_silver" class="top-[20vw] w-full" /> -->
    <div
      class="h-full overflow-y-auto overflow-x-hidden text-center w-full z-[9999] bg-[#0f132a] pt-5 px-5"
    >
      <div
        class="text-2xl font-bold mb-2"
        v-html="
          t('REFERRAL_DESC_1', {
            REWARD: settings?.referral_reward,
          })
        "
      ></div>
      <div class="text-base mb-5" v-html="t('REFERRAL_DESC_2')"></div>
      <template v-if="isLogged">
        <div class="text-sm text-center mb-2">{{ t('REFERRAL_DESC_3') }}</div>
        <div
          class="flex flex-nowrap justify-between items-center gap-2 rounded-md p-4 bg-[#2a3f84] mb-3"
          @click="handleCopy"
        >
          <div class="text-sm">
            {{ `${URL.replace('staging.', '')}/${user?.referral_code}` }}
          </div>
          <Icon name="copy" />
        </div>
        <div class="flex justify-center items-center gap-3 mb-5">
          <a
            v-for="{ icon, link, sharelink } in SOCIALS"
            :key="icon"
            :href="sharelink || link"
            target="_blank"
            rel="noopener noreferrer"
            class="row justify-center items-center"
          >
            <Icon :name="icon" :size="30" />
          </a>
        </div>
        <div class="bg-[#04081d] px-2 py-4 rounded-lg">
          <div
            class="flex justify-between items-center pb-2 mb-2"
            style="border-bottom: 1px solid #ffffff"
          >
            <div class="font-bold" v-html="t('REFERRAL_HUNTER_ID')"></div>
            <div class="font-bold" v-html="t('REFERRAL_STATUS')"></div>
          </div>
          <template v-if="!!referrals.length">
            <div class="flex flex-col gap-2">
              <div
                class="flex justify-between items-center"
                v-for="r in referrals"
                :key="r.id"
              >
                <div class="text-sm">
                  {{
                    !r.referee?.mobile_number
                      ? t('REFERRAL_GUEST')
                      : t('REFERRAL_HUNTER')
                  }}
                  #{{ r.referee?.hunter_id }}
                </div>
                <Button
                  v-if="r.status === 'pending'"
                  size="small"
                  variant="purple"
                  @click="openDialog('referral_question_mark')"
                >
                  <div class="flex items-center gap-2">
                    <div
                      class="text-sm"
                      v-html="t('REFERRAL_BUTTON_PENDING')"
                    ></div>
                    <Icon name="question-mark-white" />
                  </div>
                </Button>
                <Button
                  v-else-if="r.status === 'verified'"
                  size="small"
                  variant="secondary"
                  :loading="loading"
                  @click="handlClaim(r.id)"
                >
                  {{ t('REFERRAL_BUTTON_CLAIM') }}
                </Button>
                <Button size="small" variant="secondary" disable v-else>
                  {{ t('REFERRAL_BUTTON_CLAIMED') }}
                </Button>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="text-sm py-3" v-html="t('REFERRAL_EMPTY_LIST')"></div>
          </template>
        </div>
      </template>
      <template v-else>
        <Button
          class="mb-5"
          :label="
            t(
              (!!user?.mobile_number &&
                !user?.verified_mobile_number_at &&
                'REFERRAL_BUTTON_VERIFY') ||
                'REFERRAL_BUTTON_SIGN_UP'
            )
          "
          @click="handleSignUp"
        />

        <div
          v-if="!user?.mobile_number"
          class="text-sm underline text-link"
          v-html="t('REFERRAL_DESC_5')"
          @click="openDialog('login')"
        ></div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.referral {
  display: flex;
  flex-flow: column;
  padding: 85% 0 20px;
  background-color: #0f132a !important;

  // &.season-sg {
  //   background: linear-gradient(180deg, rgba(15, 19, 42, 0) 0%, #0f132a 14.18%),
  //     url('/imgs/kv/sg.png');
  //   background-repeat: no-repeat;
  //   background-position: center 80vw, center -10vw;
  //   background-size: 100% 150vw, 100% 112vw;
  // }
  // &.season-vn {
  //   background-size: 100% 112vw !important;
  //   background: url('/imgs/kv/vn.png') center -10vw no-repeat;
  // }

  &__bg {
    background-image: url(/imgs/capitaland-kv/ensuring-fairness-bg.png);
    width: 100%;
    height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }

  .right_referral {
    background: #462472;
    padding: 10px;
    position: absolute;
    top: 0;
    right: 14px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  &-header {
    height: 80px;
    width: 100%;

    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0 10px;
    .right {
      background: linear-gradient(180deg, rgba(35, 0, 69, 0.8) 0%, #5b1b9b 100%),
        #462472;
      border-radius: 5px;
      margin-top: 14px;
      padding: 7px 12px;
    }
  }

  &-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    background: linear-gradient(180deg, rgba(15, 19, 42, 0) 0%, #0f132a 14.18%);
  }

  &-list {
    flex: 1;
    background-color: #04081d;
    overflow: hidden;

    &__header {
      border-bottom: 1px solid #ffffff;
      padding-bottom: 8px;
    }

    &__content {
      overflow-x: hidden;
      overflow-y: auto;
    }
  }

  .text-border {
    text-shadow: rgb(65, 6, 60) 2px 0px 0px,
      rgb(65, 6, 60) 1.75517px 0.958851px 0px,
      rgb(65, 6, 60) 1.0806px 1.68294px 0px,
      rgb(65, 6, 60) 0.141474px 1.99499px 0px,
      rgb(65, 6, 60) -0.832294px 1.81859px 0px,
      rgb(65, 6, 60) -1.60229px 1.19694px 0px,
      rgb(65, 6, 60) -1.97998px 0.28224px 0px,
      rgb(65, 6, 60) -1.87291px -0.701566px 0px,
      rgb(65, 6, 60) -1.30729px -1.5136px 0px,
      rgb(65, 6, 60) -0.421592px -1.95506px 0px,
      rgb(65, 6, 60) 0.567324px -1.91785px 0px,
      rgb(65, 6, 60) 1.41734px -1.41108px 0px,
      rgb(65, 6, 60) 1.92034px -0.558831px 0px;
  }
}
</style>
