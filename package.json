{"name": "super-htm-frontend", "version": "0.0.1", "description": "Make by <PERSON><PERSON><PERSON><PERSON>", "productName": "#Huntthemouse", "author": "danhnguyen <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev --port 9000", "build": "quasar build -m pwa", "assets": "node assets.js", "xlsx": "node json-to-xlsx.js", "json": "node xlsx-to-json.js"}, "dependencies": {"@quasar/extras": "^1.17.0", "@rive-app/canvas": "^2.30.1", "@tanstack/vue-query": "^5.54.1", "@turf/bbox": "^6.5.0", "@turf/bbox-polygon": "^6.5.0", "@turf/boolean-point-in-polygon": "^6.5.0", "@turf/buffer": "^7.2.0", "@turf/circle": "^6.5.0", "@turf/distance": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/intersect": "^6.5.0", "@turf/transform-rotate": "^6.5.0", "@turf/transform-translate": "^6.5.0", "@vueuse/core": "^10.11.0", "@zxing/library": "^0.21.2", "axios": "^1.7.2", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "gsap": "^3.13.0", "howler": "^2.2.4", "libphonenumber-js": "^1.11.9", "lodash": "^4.17.21", "maplibre-gl": "^4.5.0", "ngeohash": "^0.6.3", "numeral": "^2.0.6", "pinia": "^2.1.7", "pixi.js": "7.2.4", "plyr": "^3.7.8", "posthog-js": "^1.166.1", "qrcode.vue": "^3.4.1", "quasar": "^2.18.1", "socket.io-client": "^4.7.5", "swiper": "^11.1.4", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.4", "ua-parser-js": "^1.0.39", "vee-validate": "^4.13.2", "vue": "^3.4.31", "vue-i18n": "^9.2.2", "vue-micro-route": "^4.0.8", "vue-router": "^4.0.12", "vue-turnstile": "^1.0.11", "vue3-maplibre-gl": "^3.2.4", "yup": "^1.4.0"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^3.3.1", "@quasar/app-vite": "^1.9.0", "@turf/bbox": "^6.5.0", "@turf/bbox-polygon": "^6.5.0", "@turf/boolean-point-in-polygon": "^6.5.0", "@turf/circle": "^6.5.0", "@turf/distance": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/intersect": "^6.5.0", "@turf/transform-rotate": "^6.5.0", "@turf/transform-translate": "^6.5.0", "@types/crypto-js": "^4.2.2", "@types/gsap": "^3.0.0", "@types/howler": "^2.2.11", "@types/lodash": "^4.17.6", "@types/maplibre-gl": "^1.14.0", "@types/ngeohash": "^0.6.8", "@types/node": "^12.20.21", "@types/numeral": "^2.0.5", "@types/pixi.js": "^5.0.0", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "autoprefixer": "^10.4.2", "eslint": "^8.57.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-unused-imports": "^4.0.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "typescript": "^4.9.5", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.2", "vite-plugin-checker": "^0.6.4", "vite-string-replacement": "^1.1.2", "vue-tsc": "^1.8.22", "workbox-build": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.1.0", "workbox-precaching": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}